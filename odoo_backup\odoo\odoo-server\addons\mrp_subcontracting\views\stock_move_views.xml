<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="mrp_subcontracting_stock_move_line_tree_view" model="ir.ui.view">
        <field name="name">mrp.subcontracting.stock.move.line.list.view</field>
        <field name="model">stock.move.line</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <list editable="bottom">
                <field name="company_id" column_invisible="True"/>
                <field name="product_uom_category_id" column_invisible="True"/>
                <field name="owner_id" column_invisible="True"/>
                <field name="tracking" column_invisible="True"/>
                <field name="package_id" column_invisible="True"/>
                <field name="result_package_id" column_invisible="True"/>
                <field name="location_id" column_invisible="True"/>
                <field name="location_dest_id" column_invisible="True"/>
                <field name="state" column_invisible="True"/>
                <!-- Don't put move_id here to avoid that the framework send falsy move_id -->
                <field name="id" column_invisible="True"/>
                <field name="product_id" required="1" domain="[('id', 'in', context.get('bom_product_ids'))] if context.get('is_subcontracting_portal') else []"/>
                <field name="lot_id" groups="stock.group_production_lot"
                    invisible="tracking not in ('serial', 'lot')"
                    required="tracking in ('serial', 'lot')"
                    context="{'default_product_id': product_id}"/>
                <field name="quantity"/>
                <field name="product_uom_id" groups="uom.group_uom"/>
            </list>
        </field>
    </record>
    <record id="mrp_subcontracting_portal_stock_move_line_tree_view" model="ir.ui.view">
        <field name="name">mrp.subcontracting.portal.stock.move.line.list.view</field>
        <field name="model">stock.move.line</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="mrp_subcontracting_stock_move_line_tree_view" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lot_id']" position="attributes">
                <attribute name="options">{'no_create_edit': True, 'no_open': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='product_id']" position="attributes">
                <attribute name="options">{'no_open': True}</attribute>
            </xpath>
        </field>
    </record>
    <record id="mrp_subcontracting_view_stock_move_line_operation_tree" model="ir.ui.view">
        <field name="name">mrp.subcontracting.stock.move.line.operations.list</field>
        <field name="model">stock.move.line</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lot_id']" position="attributes">
                <attribute name="options">{'no_create_edit': True, 'no_open': True}</attribute>
            </xpath>
        </field>
    </record>
    <record id="mrp_subcontracting_view_stock_move_operations" model="ir.ui.view">
        <field name="name">mrp.subcontracting.stock.move.operations.form</field>
        <field name="model">stock.move</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="stock.view_stock_move_operations" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='move_line_ids']" position="attributes">
                <attribute name="context">{'list_view_ref': 'mrp_subcontracting.mrp_subcontracting_view_stock_move_line_operation_tree', 'default_product_uom_id': product_uom, 'default_picking_id': picking_id, 'default_move_id': id, 'default_product_id': product_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}</attribute>
            </xpath>
        </field>
    </record>

    <record id="mrp_subcontracting_move_form_view" model="ir.ui.view">
        <field name="name">mrp.subcontracting.move.form.view</field>
        <field name="model">stock.move</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form create="0" delete="0">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <field name="product_uom_category_id" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="product_id" invisible="1" readonly="state == 'done'"/>
                    <field name="sequence" invisible="1"/>
                    <field name="location_id" invisible="1"/>
                    <field name="picking_id" invisible="1" readonly="state == 'done'"/>
                    <field name="location_dest_id" invisible="1"/>
                    <field name="has_tracking" invisible="1"/>
                    <field name="product_uom" invisible="1"/>
                    <field name="product_uom_qty" invisible="1" readonly="state == 'done'"/>
                    <group>
                        <field name="order_finished_lot_id"/>
                        <field name="product_uom" groups="uom.group_uom"/>
                        <field name="quantity" string="Total Consumed" readonly="1"/>
                    </group>
                    <field name="move_line_ids"
                        readonly="state in ['done', 'cancel']"
                        context="{'default_product_uom_id': product_uom, 'default_picking_id': picking_id, 'default_move_id': id, 'default_product_id': product_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}">
                        <list editable="bottom" decoration-muted="state in ('done', 'cancel')">
                            <field name="company_id" column_invisible="True"/>
                            <field name="state" column_invisible="True"/>
                            <field name="tracking" column_invisible="True"/>
                            <field name="product_uom_id" column_invisible="True"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="picking_id" column_invisible="True"/>
                            <field name="move_id" column_invisible="True"/>
                            <field name="location_id" column_invisible="True"/>
                            <field name="location_dest_id" column_invisible="True"/>
                            <field name="product_id" readonly="1" force_save="1"/>
                            <field name="quantity"/>
                            <field name="lot_id" column_invisible="parent.has_tracking not in ('serial', 'lot')" required="tracking in ('serial', 'lot')" context="{'default_product_id': product_id}" groups="stock.group_production_lot"/>
                        </list>
                    </field>
                </sheet>
            </form>
        </field>
    </record>
    <record id="mrp_subcontracting_portal_move_form_view" model="ir.ui.view">
        <field name="name">mrp.subcontracting.portal.move.form.view</field>
        <field name="model">stock.move</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="mrp_subcontracting_move_form_view" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_finished_lot_id']" position="attributes">
                <attribute name="options">{'no_open': True}</attribute>
            </xpath>
            <xpath expr="//list" position="attributes">
                <attribute name="no_open">1</attribute>
            </xpath>
            <xpath expr="//list/field[@name='lot_id']" position="attributes">
                <attribute name="options">{'no_create_edit': True, 'no_open': True}</attribute>
            </xpath>
        </field>
    </record>
    <record id="mrp_subcontracting_move_tree_view" model="ir.ui.view">
        <field name="name">mrp.subcontracting.move.list.view</field>
        <field name="model">stock.move</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <list delete="0" create="0" decoration-muted="is_done" decoration-warning="quantity - product_uom_qty &gt; 0.0001" decoration-success="not is_done and quantity - product_uom_qty &lt; 0.0001" js_class="subcontracting_portal_move_list_view">
                <field name="company_id" column_invisible="True"/>
                <field name="sequence" column_invisible="True"/>
                <field name="product_uom_category_id" column_invisible="True"/>
                <field name="name" column_invisible="True"/>
                <field name="unit_factor" column_invisible="True"/>
                <field name="date" column_invisible="True"/>
                <field name="picking_type_id" column_invisible="True"/>
                <field name="has_tracking" column_invisible="True"/>
                <field name="operation_id" column_invisible="True"/>
                <field name="is_done" column_invisible="True"/>
                <field name="bom_line_id" column_invisible="True"/>
                <field name="location_id" column_invisible="True"/>
                <field name="warehouse_id" column_invisible="True"/>
                <field name="product_uom_qty" column_invisible="True" readonly="state == 'done'"/>
                <field name="location_dest_id" column_invisible="True"/>
                <field name="state" column_invisible="True" force_save="1"/>
                <field name="raw_material_production_id" column_invisible="True"/>
                <field name="product_id" required="1" readonly="state == 'done'"/>
                <field name="order_finished_lot_id"/>
                <field name="quantity" string="Consumed" readonly="1"/>
                <field name="product_uom" groups="uom.group_uom"/>
            </list>
        </field>
    </record>
    <record id="view_move_search" model="ir.ui.view">
        <field name="name">stock.move.search</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_move_search" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="order_finished_lot_id"/>
            </xpath>
        </field>
    </record>

</odoo>
