<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="mrp.MrpTimer">
        <span t-esc="durationFormatted"/>
    </t>

    <t t-name="mrp.MrpTimerField">
        <MrpTimer t-if="props.readonly" value="duration" ongoing="ongoing"/>
        <input t-else="" t-att-id="props.id" t-ref="numpadDecimal" t-att-placeholder="props.placeholder" inputmode="numeric" class="o_input" />
    </t>
</templates>
