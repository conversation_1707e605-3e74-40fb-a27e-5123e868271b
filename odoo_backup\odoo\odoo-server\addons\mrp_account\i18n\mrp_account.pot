# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2025-05-07 20:37+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$1000"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$5000"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
msgid "%s - Labour"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "2023-08-15"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_move_form_inherit_mrp_account
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span style=\"margin-right: 15px;\">Total</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Amount</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Date</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Product</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Quantity</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Ref.</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Unit of Measure</span>"
msgstr ""

#. module: mrp_account
#: model:ir.model.constraint,message:mrp_account.constraint_mrp_account_wip_accounting_line_check_debit_credit
msgid "A single line cannot be both credit and debit."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__account_id
msgid "Account"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__account_move_line_id
msgid "Account Move Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting_line
msgid "Account move line to be created when posting WIP account move"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Acme Corp."
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_account
msgid "Analytic Account"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution
msgid "Analytic Distribution"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Bills of Materials"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
msgid "Bom"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr ""

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_ids
msgid "Costs Hour Account"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_uid
msgid "Created by"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_date
msgid "Created on"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__credit
msgid "Credit"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__currency_id
msgid "Currency"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__date
msgid "Date"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__debit
msgid "Debit"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Discard"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__display_name
msgid "Display Name"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__expense_account_id
msgid "Expense Account"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__id
msgid "ID"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__journal_id
msgid "Journal"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__label
msgid "Label"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Laptop"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manual Entry"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_applicability__business_domain__manufacturing_order
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Manufacturing Orders"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_count
msgid "Manufacturing Orders Count"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manufacturing WIP - %(orders_list)s"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__mo_ids
msgid "Mo"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_ids
msgid "Mo Analytic Account Line"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid ""
"Please make sure the total credit amount equals the total debit amount."
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post Manufacturing WIP"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post WIP"
msgstr ""

#. module: mrp_account
#: model:ir.actions.act_window,name:mrp_account.action_wip_accounting
msgid "Post WIP Accounting Entry"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
msgid "Production"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "REF123"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reference
msgid "Reference"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_ids
msgid "Relevant WIP MOs"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reversal_date
msgid "Reversal Date"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal date must be after the posting date."
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal of: %s"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,help:mrp_account.field_account_move__wip_production_ids
msgid ""
"The MOs that this WIP entry was based on. Expected to be set at time of WIP "
"entry creation."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__expense_account_id
msgid ""
"The expense is accounted for when the manufacturing order is marked as done."
" If not set, it is the expense account of the final product that will be "
"used instead."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will remain on the account once the production is completed."
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Credit"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Debit"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Units"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Component Value"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Overhead"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/account_move.py:0
msgid "WIP MOs of %s"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "WIP Report for"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__line_ids
msgid "WIP accounting lines"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__wip_accounting_id
msgid "WIP accounting wizard"
msgstr ""

#. module: mrp_account
#: model:ir.actions.report,name:mrp_account.wip_report
msgid "WIP report"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_ids
msgid "Wc Analytic Account Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting
msgid "Wizard to post Manufacturing WIP account move"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
msgid "Work Orders"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenter"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
msgid "[WC] %s"
msgstr ""
