# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onboarding
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:57+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Odoo哥 <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "#{alt}"
msgstr "#{alt}"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__progress_ids
msgid "All Onboarding Progress Records (across companies)."
msgstr "所有入职进度记录（跨公司）"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "All done!"
msgstr "全部搞定！"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "All related Onboarding Progress Step Records (across companies)"
msgstr "所有相关的入职进度步骤记录（跨公司）"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image_alt
msgid "Alt Text for the Step Image"
msgstr "步骤图片的 Alt 文本"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
msgid ""
"An \"Opening Action\" is required for the following steps to be linked to an"
" onboarding panel: %(step_titles)s"
msgstr "要将下列步骤与新手简介面板相关联，必须有 \"打开操作\"：%(step_titles)s"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Are you sure you want to hide these configuration steps?"
msgstr "您确定要隐藏这些配置步骤吗？"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__button_text
msgid "Button text"
msgstr "按钮文本"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Cancel"
msgstr "取消"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close"
msgstr "关闭"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close Panel"
msgstr "关闭面板"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Close the onboarding panel"
msgstr "关闭正在进行的面板"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Closing action"
msgstr "结业动作"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__company_id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__company_id
msgid "Company"
msgstr "公司"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_onboarding_state
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_step_state
msgid "Completion State"
msgstr "完成状态"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_uid
msgid "Created by"
msgstr "创建人"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_date
msgid "Created on"
msgstr "创建日期"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__description
msgid "Description"
msgstr "描述"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__done
msgid "Done"
msgstr "已完成"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_icon
msgid "Font Awesome Icon when completed"
msgstr "当完成Font Awesome图标"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Get them out of my sight!"
msgstr "让他们离开我的视线"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_container
msgid "Hide Onboarding Tips"
msgstr "隐藏入职提示"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__id
msgid "ID"
msgstr "ID"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__is_per_company
msgid "Is per company"
msgstr "是公司"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__just_done
msgid "Just done"
msgstr "已完成"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "Let's do it"
msgstr "让我们开始吧"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__text_completed
msgid "Message at completion"
msgstr "完成时的信息"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__name
msgid "Name of the onboarding"
msgstr "入职名称"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Name of the onboarding model action to execute when closing the panel."
msgstr "关闭面板时要执行的入职模型动作的名称。"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid ""
"Name of the onboarding step model action to execute when opening the step, "
"e.g. action_open_onboarding_1_step_1"
msgstr "打开步骤时要执行的入职步骤模型动作的名称，例如action_open_onboarding_1_step_1"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding.py:0
msgid "Nice work! Your configuration is done."
msgstr "干得好!设置已完成."

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__not_done
msgid "Not done"
msgstr "未完成"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding
msgid "Onboarding"
msgstr "新手入门"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress"
msgstr "入职进度"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__progress_ids
msgid "Onboarding Progress Records"
msgstr "入职进度记录"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "Onboarding Progress Step Records"
msgstr "入职进度步骤记录"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress_step
msgid "Onboarding Progress Step Tracker"
msgstr "入职进度步骤跟踪表"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Onboarding Progress Step for the current context (company)."
msgstr "当前内容 (公司) 的入职进度步骤。"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress
msgid "Onboarding Progress Tracker"
msgstr "入职进度跟踪表"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress for the current context (company)."
msgstr "当前内容 (公司) 的入职进度。"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding_step
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_id
msgid "Onboarding Step"
msgstr "入门步骤"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_state
msgid "Onboarding Step Progress"
msgstr "入职进度步骤"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_step
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_tree
msgid "Onboarding Steps"
msgstr "入职步骤"

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_onboarding_route_name_uniq
msgid "Onboarding alias must be unique."
msgstr "入职别名必须是唯一的。"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_state
msgid "Onboarding progress"
msgstr "入职进度"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__step_ids
msgid "Onboarding steps"
msgstr "入职步骤"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__onboarding_ids
#: model:ir.ui.menu,name:onboarding.menu_onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Onboardings"
msgstr "入职"

#. module: onboarding
#: model:ir.ui.menu,name:onboarding.menu_onboarding_step
msgid "Onboardings Steps"
msgstr "入职步骤"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__route_name
msgid "One word name"
msgstr "单字的名称"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid "Opening action"
msgstr "开业动作"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__progress_step_ids
msgid "Progress Steps Trackers"
msgstr "入职步骤跟踪表"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__progress_ids
msgid "Related Onboarding Progress Tracker"
msgstr "相关的入职进度跟踪器"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_id
msgid "Related onboarding tracked"
msgstr "相关的入职跟踪"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__sequence
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__sequence
msgid "Sequence"
msgstr "序列"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_per_company
msgid "Should be done per company?"
msgstr "每家公司都应该完成吗？"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__step_image_alt
msgid "Show when impossible to load the image"
msgstr "无法加载图像时显示"

#. module: onboarding
#. odoo-python
#: code:addons/onboarding/models/onboarding_onboarding_step.py:0
msgid "Step Completed!"
msgstr "步骤已完成！"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image
msgid "Step Image"
msgstr "步骤图片"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__step_image_filename
msgid "Step Image Filename"
msgstr "步骤图片文件名"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Step Progress"
msgstr "步骤进展"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
msgid "Steps"
msgstr "步骤"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__button_text
msgid "Text on the panel's button to start this step"
msgstr "开始此步骤的面板按钮上的文本"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__text_completed
msgid "Text shown on onboarding when completed"
msgstr "完成时文本显示在新手简介"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_text
msgid "Text to show when step is completed"
msgstr "步骤完成时显示的文本"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__title
msgid "Title"
msgstr "标题"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Toggle visibility"
msgstr "切换可见性"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_onboarding_closed
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__is_onboarding_closed
msgid "Was panel closed?"
msgstr "面板关闭了吗？"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_step
msgid "o_onboarding_confetti"
msgstr "o_onboarding_confetti"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_panel
msgid "onboarding.onboarding.step"
msgstr "onboarding.onboarding.step"
