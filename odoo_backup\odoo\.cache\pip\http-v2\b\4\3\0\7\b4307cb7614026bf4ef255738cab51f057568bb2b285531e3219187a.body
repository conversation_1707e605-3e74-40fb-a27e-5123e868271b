Metadata-Version: 2.1
Name: polib
Version: 1.1.1
Summary: A library to manipulate gettext files (po and mo files).
Home-page: https://github.com/izimobil/polib/
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON>-email: <EMAIL>
License: MIT
Download-URL: https://pypi.python.org/packages/source/p/polib/polib-1.1.1.tar.gz
Platform: posix
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.0
Classifier: Programming Language :: Python :: 3.1
Classifier: Programming Language :: Python :: 3.2
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Internationalization
Classifier: Topic :: Software Development :: Localization
Classifier: Topic :: Text Processing :: Linguistic

=====
polib
=====

|build-status-image| |codecov-image| |documentation-status-image| |pypi-version| |py-versions|

Overview
--------

polib is a library to manipulate, create, modify gettext files (pot, po and mo
files). You can load existing files, iterate through it's entries, add, modify
entries, comments or metadata, etc... or create new po files from scratch.

polib supports out of the box any version of python ranging from 2.7 to latest
3.X version.

polib is pretty stable now and is used by many 
`opensource projects <http://polib.readthedocs.org/en/latest/projects.html>`_.

The project code and bugtracker is hosted on 
`Github <https://github.com/izimobil/polib/>`_.

polib is generously documented, you can `browse the documentation online 
<http://polib.readthedocs.org/>`_, a good start is to read 
`the quickstart guide  <http://polib.readthedocs.org/en/latest/quickstart.html>`_.


Installation
~~~~~~~~~~~~

Just use ``pip``:

.. code:: bash

    $ pip install polib


Basic example
~~~~~~~~~~~~~

.. code:: python

    import polib

    pofile = polib.pofile('/path/to/pofile.po')

    for entry in pofile:
        print(entry.msgid, entry.msgtxt)


.. |build-status-image| image:: https://secure.travis-ci.org/izimobil/polib.svg?branch=master
   :target: http://travis-ci.org/izimobil/polib?branch=master
   :alt: Travis build

.. |codecov-image| image:: https://codecov.io/gh/izimobil/polib/branch/master/graph/badge.svg
  :target: https://codecov.io/gh/izimobil/polib

.. |pypi-version| image:: https://img.shields.io/pypi/v/polib.svg
   :target: https://pypi.python.org/pypi/djangorestframework-datatables
   :alt: Pypi version

.. |documentation-status-image| image:: https://readthedocs.org/projects/polib/badge/?version=latest
   :target: http://polib.readthedocs.io/en/latest/?badge=latest
   :alt: Documentation Status

.. |py-versions| image:: https://img.shields.io/pypi/pyversions/polib.svg
   :target: https://img.shields.io/pypi/pyversions/polib.svg
   :alt: Python versions


=========
Changelog
=========

Version 1.1.1 (2021/03/26)
--------------------------
 - Strip UTF-16 BOM (U+FEFF) from start of file
 - Message context (msgctxt) support for MO files
 - Dropped Python < 2.7 support
 - Updated docs and README after switch from Bitbucket to Github

Version 1.1.0 (2017/11/27)
--------------------------
 - Fixed entries sorting when generating mo file (Fixes #78)
 - Fixed find method (Fixes #84)
 - Refactored POEntry.__cmp__ method (this should fix issues #60, #65 and #79)
 - Fixed duplicated entries when merging po file with pot file (fixes #68)
 - Fixed "None" string being outputted when polib deals with syntax error in string (Fixes issue #66)
 - Added a fuzzy property to entries (Fixes #76)
 - Take into account the message context when comparing entries
 - Leave occurence untouched if line number is not a number (fixes #80 and #90)
 - Fixed test for mo files that was failing because since gettext version ********, msgfmt skips the "POT-Creation-Date" metadata entry (Fixes #86)
 - Fixed major revision number comparison
 - Dropped python 2.4 support

Version 1.0.8 (2016/11/21)
--------------------------
 - Fixed issue #70 (occurrences parsing for windows pathes)
 - Fixed issue #71 (tcomment and flags not saved for obsolete entries)
 - Fixed issue #72 (wrong metadata ordering)
 - Fixed issue #73 (can't always unpickle POFile)
 - Use natural sorting for additional headers (metadata)
 - Fixed typos in various documents

Version 1.0.7 (2015/07/08)
--------------------------
 - Fixed bad parsing of indented msgstr_plural
 - Fixed ordering of "Language" metadata entry
 - Removed space after "#" in header if comment line is empty (like gettext tools)
 - Fixed typos / grammar errors (thanks Jakub Wilk)
 - Take into account msgid_plural if needed when comparing entries (thanks Leonardo Constantino Oliveira)
 - Fixed issue #63 (str() on a bytes instance when using python3) (thanks Jakub Wilk)

Version 1.0.6 (2015/01/04)
--------------------------
 - Wheel support
 - Add missing 'Language' and 'Plural-Forms' to metadata ordering
 - More accurate float operation for POFile.percent_translated()

Version 1.0.5 (2014/08/22)
--------------------------
 - Fixed issue #59: tokens variable referenced before assignment
 - Implemented feature request #56: line number information in PO entries
 - Fixed issue #61: polib does not handle previous msgid on multilines properly

Version 1.0.4 (2014/02/19)
--------------------------
 - Fixed issue #43: improved check that determine if polib is dealing with a filepath or unicode content
 - Fixed issue #44: polib now checks MO files revision number and throws an error if the number is unexpected
 - Fixed issue #45: parse properly mo files with no header entry
 - Fixed issue #47: added flags attribute for MOEntry to be consistent with POEntry
 - Fixed issue #49: use integers rather than strings for msgstr_plural keys
 - Fixed issue #51: if a PO file ends with a comment, polib adds a spurious empty entry at the end
 - Fixed issue #52: bad magic number written on big endian platforms
 - Fixed issue #53: added a __hash__() method to POEntry and MOEntry classes
 - Fixed issue #54: use lowercase for state identifiers. This fixes issues with certain locales and string.lower()
 - Fixed issue #58: use io.open() instead of codecs.open() because the latter doesn't handle very well universal line endings
 - Make sure the mo file is closed at garbage collection, this prevents warnings on unclosed file when running tests with python >= 3.2
 - Better way to test endianness
 - polib download URL is now on Pypi

Version 1.0.3 (2013/02/09)
--------------------------
 - Fixed issue #38: POFile.append() raised a duplicate exception when you tried to add a new entry with the same msgid and a different msgctxt (only when check_for_duplicates option is set to True)
 - Fixed issue #39: Added __init__.py file for convenience
 - Fixed issue #41: UnicodeDecodeError when running setup.py build on python3 with C locale
 - polib is now fully PEP8 compliant
 - Small improvements: remove unused "typ" var (thanks Rodrigo Silva), improved Makefile, Make sure _BaseFile.__contains__ returns a boolean value

Version 1.0.2 (2012/10/23)
--------------------------
 - allow empty comments, flags or occurrences lines

Version 1.0.1 (2012/09/11)
--------------------------
 - speed up POFile.merge method (thanks @encukou)
 - allow comments starting with two '#' characters (thanks @goibhniu)

Version 1.0.0 (2012/06/08)
--------------------------
Yeah... after nearly 6 years, polib reaches the stable state :)
Changes and fixes in this release :

 - polib.pofile and polib.mofile functions can now return a custom class (thanks Craig Blaszczyk)
 - polib now can find the metadata entry no matter where it is located (thanks François Poirotte)
 - fixed issue #28 (IOError on reading obsolete "previous msgid" entries) (thanks James Ni)

Version 0.7.0 (2011/07/14)
--------------------------
This version adds support for python 3 (thanks to Vinay Sajip).
polib now supports out-of-the-box any version of python ranging from 2.4 to latest 3.X version.
polib is now 5 years old ;) so the 0.7.X branch will be the last before the 1.X stable branch.

Version 0.6.4 (2011/07/13)
--------------------------
 - Better api, autodetected_encoding is no longer required to explicitly set the encoding (fixes issue #23),
 - Fixed issue #24 Support indented PO files (thanks to François Poirotte).

Version 0.6.3 (2011/02/19)
--------------------------
 - Fixed issue #19 (Disappearing newline characters due to textwrap module),
 - ensure wrapping works as expected.

Version 0.6.2 (2011/02/09)
--------------------------
 - Backported textwrap.TextWrapper._wrap_chunks that has support for the drop_whitespace parameter added in Python 2.6 (Fixes #18: broken compatibility with python 2.5, thanks @jezdez).

Version 0.6.1 (2011/02/09)
--------------------------
 - fixed regression that prevented POFile initialization from data to work (issue #17).

Version 0.6.0 (2011/02/07)
--------------------------
 - polib is now `fully documented <http://polib.readthedocs.org>`_,
 - switched from doctests to unit tests to keep the polib.py file clean,
 - fixed issue #7 (wrapping issues, thanks @jezdez),
 - added a __eq__ method to _BaseFile (thanks @kost BebiX),
 - handle msgctxt correctly when compiling mo files,
 - compiled mo files are now exactly the same as those compiled by msgfmt without using hash tables.

Version 0.5.5 (2010/10/30)
--------------------------
 - Removed multiline handling code, it was a mess and was the source of potential bugs like issue #11,
 - Fixed typo in README and CHANGELOG, fixes issue #13.

Version 0.5.4 (2010/10/02)
--------------------------
 - fixed an issue with detect_encoding(), in some cases it could return an invalid charset.

Version 0.5.3 (2010/08/29)
--------------------------
 - correctly unescape lines containing both \\\\n and \\n (thanks to Martin Geisler),
 - fixed issue #6: __str__() methods are returning unicode instead of str,
 - fixed issue #8: POFile.merge error when an entry is obsolete in a .po, that this entry reappears in the .pot and that we merge the two,
 - added support to instantiate POFile objects using data instead of file path (thanks to Diego Búrigo Zacarão),
 - fixed issue #9: POFile.merge drop fuzzy attributes from translations (thanks to Tim Gerundt),
 - fixed issue #10: Finding entries with the same msgid and different context (msgctxt).

Version 0.5.2 (2010/06/09)
--------------------------
 - fixed issue #1: untranslated_entries() also show fuzzy message,
 - write back the fuzzy header if present in the pofile,
 - added support for previous msgctxt, previous msgid and previous msgid_plural comments (fixes issue #5),
 - better handling of lines wrapping.

Version 0.5.1 (2009/12/14)
--------------------------
 - fixed issue #0025: setup.py requires CHANGELOG but it's not present in polib-0.5.0-tar.gz

Version 0.5.0 (2009/12/13)
--------------------------
 - fixed issue #0017: UnicodeDecodeError while writing a mo-file,
 - fixed issue #0018: implemented support for msgctxt,
 - fixed bug when compiling plural msgids/strs,
 - API docs are no longer included, hopefully next release will ship with sphinx documentation,
 - parse msg plural entries correctly when reading mo files,
 - fixed issue #0020 and #0021: added ability to check for duplicate when adding entries to po/mo files, this is optional and not enabled by default because it slows down considerably the library,
 - fixed issue #0022: unescaping code is insufficient,
 - fixed issue #0023: encoding error when saving mo file as po file (thanks to sebastien.sable for the patch !).

Version 0.4.2 (2009/06/05)
--------------------------
 - fixed issue #0007: use the codecs module to open files,
 - fixed issue #0014: plural forms are not saved correctly in the mo file (thanks lorenzo.gil.sanchez for the patch),
 - fixed issue #0015: no LICENSE file included in tarball,  
 - removed Version/Date from README,
 - added test pot files to MANIFEST.in,
 - performance improvement in find() method (thanks Thomas !).

Version 0.4.1 (2009/03/04)
--------------------------
 - fixed issue #0006: plural msgstrs were saved unsorted,
 - fixed issue #0008: long comment lines broke 'save()' method,
 - removed performance shortcuts: they were in fact inefficient, I was mislead by the python profile module, kudos to Thomas for making me realise that,
 - fixed issue #0010: wrong polib version number,
 - fixed issue #0011: occurrences parsing is now more robust and can handle weird references formats (like in eToys OLPC po files),
 - fixed issue #0012: improved merge() method.

Version 0.4.0 (2008/11/26)
--------------------------
 - fixed bug #0005: percent_translated divide by 0 on empty po files,
 - fixed bug #0004: occurrences that have hyphens are wrapped when they should not,
 - changes in how encoding is handled,
 - remove deprecation warnings for typo on "occurrences",
 - added POEntry.__cmp__() method to sort entries like gettext does,
 - fixed POEntry.transalated(),
 - added a merge() method to POFile class, that behaves like the gettext msgmerge utility,
 - obsolete entries are now written at the end of the file and with only msgid/msgstr like gettext does,
 - fixed some bugs in mo files parsing,
 - renamed quote/unquote functions to escape/unescape,
 - various cosmetic changes.

Version 0.3.1 (2007/12/13)
--------------------------
 - fixed bug #0002: typo on "occurrences",
 - fixed bug #0003: mismatch in exception instance names,
 - removed deprecation warnings,
 - removed unused charset() method in POFile/MOFile objects,
 - fixed bug in multibytes string length (added regression tests),
 - fixed a bug in detect_encoding(),
 - added a find() method to _BaseFile class,
 - proper handling of quoting and unquoting,
 - proper handling of multiline strings in metadata 

Version 0.3.0 (2007/10/17)
--------------------------
 - speed improvements,
 - polib can now compile mo files,
 - unicode support,
 - fixed bug #0001: global name 'sorted' is not defined" on python 2.3.

Version 0.1.0 (2006-08-08)
--------------------------
Initial release




