<?xml version="1.0" encoding="UTF-8"?>
<templates>

    <div t-name="mrp.workorderPopover">
        <h6>Scheduling Information</h6>
        <t t-foreach="props.infos" t-as="info" t-key="info_index">
            <i t-attf-class="oi oi-arrow-right me-2 #{ info.color }"></i><t t-esc="info.msg"/><br/>
        </t>
        <button t-if="props.replan" t-on-click="onReplanClick" class="btn btn-sm btn-primary m-1 float-end action_replan_button">Replan</button>
    </div>

</templates>
