<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.rule" id="analytic_accout_subcontractor_rule">
        <field name="name">Analytic Account Subcontractor</field>
        <field name="model_id" ref="mrp_account.model_account_analytic_account"/>
        <field name="domain_force">[('bom_ids', 'in', user.partner_id.commercial_partner_id.bom_ids.ids)]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <record model="ir.rule" id="analytic_accout_line_subcontractor_rule">
        <field name="name">Analytic Account Line Subcontractor</field>
        <field name="model_id" ref="mrp_account.model_account_analytic_line"/>
        <field name="domain_force">[('account_id.bom_ids', 'in', user.partner_id.commercial_partner_id.bom_ids.ids)]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

</odoo>
