import pandas as pd

# 读取原始Excel文件
df = pd.read_excel('下载进度.xlsx')

print('=== 创建完成数量统计表 ===')

# 统计各种状态的数量
completed_count = len(df[df['状态'] == '已完成'])
finished_count = len(df[df['状态'] == '完成'])
total_completed = completed_count + finished_count

# 创建统计汇总表
summary_data = {
    '状态类型': ['已完成', '完成', '总计'],
    '数量': [completed_count, finished_count, total_completed]
}

summary_df = pd.DataFrame(summary_data)

# 保存统计汇总表
summary_file = '完成数量统计表.xlsx'
summary_df.to_excel(summary_file, index=False)

print(f'完成数量统计表已保存到: {summary_file}')
print('\n=== 统计结果 ===')
print(summary_df.to_string(index=False))

print(f'\n最终已完成项目总数: {total_completed}项')
