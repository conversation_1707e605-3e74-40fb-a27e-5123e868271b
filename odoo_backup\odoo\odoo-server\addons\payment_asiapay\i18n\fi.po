# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_asiapay
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (success code "
"%(success_code)s; primary response code %(response_code)s). Please try "
"again."
msgstr ""
"Maksun käsittelyssä tapahtui virhe (onnistumiskoodi %(success_code)s; "
"ensisijainen vastauskoodi %(response_code)s). Yritä uudelleen."

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__code__asiapay
msgid "AsiaPay"
msgstr "AsiaPay"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_merchant_id
msgid "AsiaPay Merchant ID"
msgstr "AsiaPay-kauppiastunnus"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_secure_hash_function
msgid "AsiaPay Secure Hash Function"
msgstr "AsiaPay Secure Hash Function"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_secure_hash_secret
msgid "AsiaPay Secure Hash Secret"
msgstr "AsiaPay Secure Hash Salaisuus"

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_provider.py:0
msgid "AsiaPay does not support the following currencies: %(currencies)s."
msgstr "AsiaPay ei tue seuraavia valuuttoja: %(currencies)s."

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_brand
msgid "Asiapay Brand"
msgstr "Asiapay-brändi"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__bimopay
msgid "BimoPay"
msgstr "BimoPay"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Brand"
msgstr "Merkki"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__code
msgid "Code"
msgstr "Koodi"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Merchant ID"
msgstr "Kauppiastunnus"

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Viitettä %s vastaavaa tapahtumaa ei löytynyt."

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_provider.py:0
msgid "Only one currency can be selected by AsiaPay account."
msgstr "AsiaPay-tilillä voidaan valita vain yksi valuutta."

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__paydollar
msgid "PayDollar"
msgstr "PayDollar"

#. module: payment_asiapay
#: model:ir.model,name:payment_asiapay.model_payment_provider
msgid "Payment Provider"
msgstr "Maksupalveluntarjoaja"

#. module: payment_asiapay
#: model:ir.model,name:payment_asiapay.model_payment_transaction
msgid "Payment Transaction"
msgstr "Maksutapahtuma"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__pesopay
msgid "PesoPay"
msgstr "PesoPay"

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr "Vastaanotetut tiedot, joista puuttuu viite %(ref)s."

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "Received data with missing success code."
msgstr "Vastaanotetut tiedot, joista puuttuu onnistumiskoodi."

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_secure_hash_function__sha1
msgid "SHA1"
msgstr "SHA1"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_secure_hash_function__sha256
msgid "SHA256"
msgstr "SHA256"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_secure_hash_function__sha512
msgid "SHA512"
msgstr "SHA512"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Secure Hash Function"
msgstr "Turvallinen hash-funktio"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Secure Hash Secret"
msgstr "Turvallinen hash-alaisuus"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__siampay
msgid "SiamPay"
msgstr "SiamPay"

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__asiapay_merchant_id
msgid "The Merchant ID solely used to identify your AsiaPay account."
msgstr ""
"Kauppiastunnus, jota käytetään ainoastaan AsiaPay-tilisi tunnistamiseen."

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__asiapay_brand
msgid "The brand associated to your AsiaPay account."
msgstr "AsiaPay-tiliisi liittyvä tuotemerkki."

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__asiapay_secure_hash_function
msgid "The secure hash function associated to your AsiaPay account."
msgstr "AsiaPay-tiliisi liittyvä turvallinen hash-funktio."

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Tämän maksupalveluntarjoajan tekninen koodi."

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "Unknown success code: %s"
msgstr "Tuntematon onnistumiskoodi: %s"
