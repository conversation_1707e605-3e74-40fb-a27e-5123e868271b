# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil O<PERSON>o, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$1000"
msgstr "$ 1000"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$5000"
msgstr "$ 5000"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
msgid "%s - Labour"
msgstr "%s - Arbeid"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "2023-08-15"
msgstr "15-08-2023"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_move_form_inherit_mrp_account
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Productie</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">Waardering</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span style=\"margin-right: 15px;\">Total</span>"
msgstr "<span style=\"margin-right: 15px;\">Totaal</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Amount</span>"
msgstr "<span>Bedrag</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Date</span>"
msgstr "<span>Datum</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Product</span>"
msgstr "<span>Product</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Quantity</span>"
msgstr "<span>Aantal</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Ref.</span>"
msgstr "<span>Ref.</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Unit of Measure</span>"
msgstr "<span>Maateenheid</span>"

#. module: mrp_account
#: model:ir.model.constraint,message:mrp_account.constraint_mrp_account_wip_accounting_line_check_debit_credit
msgid "A single line cannot be both credit and debit."
msgstr "Eén regel kan niet zowel credit als debet zijn."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__account_id
msgid "Account"
msgstr "Rekening"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__account_move_line_id
msgid "Account Move Line"
msgstr "Boekingsregel"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting_line
msgid "Account move line to be created when posting WIP account move"
msgstr ""
"Boekingsregel die moet worden aangemaakt bij het boeken van een WIP-boeking"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Acme Corp."
msgstr "Acme Corp."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "Analytische rekening"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analytisch verdeelmodel"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytische boeking"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Toepassingen van analytische dimensies"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_precision
msgid "Analytic Precision"
msgstr "Analytische nauwkeurigheid"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Bills of Materials"
msgstr "Stuklijsten"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "Aantal stuklijsten"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
msgid "Bom"
msgstr "Stuklijst"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Categorie"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "Bereken kostprijs vanuit stuklijst"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Bereken de prijs van het product gebruikmakend van producten en bewerkingen "
"gerelateerd aan de stuklijst, enkel voor geproduceerde producten."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_ids
msgid "Costs Hour Account"
msgstr "Kosten per uur rekening"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__credit
msgid "Credit"
msgstr "Credit"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__date
msgid "Date"
msgstr "Datum"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__debit
msgid "Debit"
msgstr "Debet"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Discard"
msgstr "Negeren"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Distributie analytisch rekening"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domein"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__expense_account_id
msgid "Expense Account"
msgstr "Kostenrekening"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr "Extra eenheidkosten"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__id
msgid "ID"
msgstr "ID"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__journal_id
msgid "Journal"
msgstr "Dagboek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__label
msgid "Label"
msgstr "Omschrijving"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Laptop"
msgstr "Laptop"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Productieorderoverzicht"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manual Entry"
msgstr "Handmatige post"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_applicability__business_domain__manufacturing_order
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Productieorder"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Manufacturing Orders"
msgstr "Productieorders"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_count
msgid "Manufacturing Orders Count"
msgstr "Aantal productieorders"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manufacturing WIP - %(orders_list)s"
msgstr "Productie WIP - %(orders_list)s"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__mo_ids
msgid "Mo"
msgstr "Po"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_ids
msgid "Mo Analytic Account Line"
msgstr "Analytische rekening productieorderregel"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid ""
"Please make sure the total credit amount equals the total debit amount."
msgstr ""
"Zorg ervoor dat het totale creditbedrag gelijk is aan het totale "
"debetbedrag."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post Manufacturing WIP"
msgstr "Boek WIP productie"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post WIP"
msgstr "Boek WIP"

#. module: mrp_account
#: model:ir.actions.act_window,name:mrp_account.action_wip_accounting
msgid "Post WIP Accounting Entry"
msgstr "Boek WIP-boeking"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr "Product"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr "Productcategorie"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr "Productvariant"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
msgid "Production"
msgstr "Productie"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr "Productierekening"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "REF123"
msgstr "REF123"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reference
msgid "Reference"
msgstr "Referentie"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_ids
msgid "Relevant WIP MOs"
msgstr "Relevante WIP productieorders"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reversal_date
msgid "Reversal Date"
msgstr "Creditdatum"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal date must be after the posting date."
msgstr "De terugboekingsdatum moet na de boekingsdatum liggen."

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal of: %s"
msgstr "Omkering van: %s"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "Toon waardering"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Voorraadverplaatsing"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "Voorraadwaardelaag"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,help:mrp_account.field_account_move__wip_production_ids
msgid ""
"The MOs that this WIP entry was based on. Expected to be set at time of WIP "
"entry creation."
msgstr ""
"De MO's waarop deze WIP-boeking is gebaseerd. Wordt naar verwachting "
"ingesteld bij het aanmaken van de WIP-boeking."

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__expense_account_id
msgid ""
"The expense is accounted for when the manufacturing order is marked as done."
" If not set, it is the expense account of the final product that will be "
"used instead."
msgstr ""
"De kosten worden geboekt wanneer de productieorder gereed wordt gemeld. De "
"kostenrekening van het eindproduct wordt gebruikt als deze niet is "
"ingesteld."

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will remain on the account once the production is completed."
msgstr ""
"Deze rekening wordt gebruikt als een waarderingstegenrekening voor onderdelen en eindproducten voor productieorders. \n"
"Als er werkplek-/werknemerskosten zijn, blijft deze waarde op de rekening totdat de productie voltooid is."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Credit"
msgstr "Totaal credit"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Debit"
msgstr "Totaal debet"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Units"
msgstr "Stuk(s)"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Component Value"
msgstr "WIP - Componentwaarde"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Overhead"
msgstr "WIP - Overhead"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/account_move.py:0
msgid "WIP MOs of %s"
msgstr "WIP productieorders van %s"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "WIP Report for"
msgstr "WIP rapportage voor"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__line_ids
msgid "WIP accounting lines"
msgstr "WIP-boekhoudregels"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__wip_accounting_id
msgid "WIP accounting wizard"
msgstr "Wizard voor WIP-boekhouding"

#. module: mrp_account
#: model:ir.actions.report,name:mrp_account.wip_report
msgid "WIP report"
msgstr "WIP rapportage"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_ids
msgid "Wc Analytic Account Line"
msgstr "Analytische rekening werkplek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting
msgid "Wizard to post Manufacturing WIP account move"
msgstr "Wizard om productie WIP-account te verplaatsen"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "Werkplek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Gebruik werkplek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "Werkorder"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "Aantal werkorders"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
msgid "Work Orders"
msgstr "Werkorders"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenter"
msgstr "Werkplek"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Werkplek effectiviteitslog"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
msgid "[WC] %s"
msgstr "[WP] %s"
