# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_repair
# 
# Translators:
# Hed <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: mrp_repair
#: model:ir.model.fields,field_description:mrp_repair.field_repair_order__production_count
msgid "Count of MOs generated"
msgstr "מספר הוראות ייצור שנוצרו"

#. module: mrp_repair
#: model:ir.model.fields,field_description:mrp_repair.field_mrp_production__repair_count
msgid "Count of source repairs"
msgstr "ספירה של תיקוני מקור"

#. module: mrp_repair
#: model_terms:ir.ui.view,arch_db:mrp_repair.view_repair_order_form_inherit
msgid "Manufacturing"
msgstr "ייצור"

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_mrp_production
msgid "Manufacturing Order"
msgstr "הוראת ייצור"

#. module: mrp_repair
#. odoo-python
#: code:addons/mrp_repair/models/repair.py:0
msgid "Manufacturing Orders generated by %s"
msgstr "הוראות ייצור נוצרו ע\"י %s"

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_repair_order
msgid "Repair Order"
msgstr "הזמנת תיקון"

#. module: mrp_repair
#. odoo-python
#: code:addons/mrp_repair/models/production.py:0
msgid "Repair Source of %s"
msgstr "מקור תיקון של %s"

#. module: mrp_repair
#: model_terms:ir.ui.view,arch_db:mrp_repair.mrp_production_form_view_inherit
msgid "Repairs"
msgstr "תיקונים"

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_stock_move
msgid "Stock Move"
msgstr "תנועת מלאי"
