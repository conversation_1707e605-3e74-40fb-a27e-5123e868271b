<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_location_form" model="ir.ui.view">
        <field name="name">stock.location.form</field>
        <field name="model">stock.location</field>
        <field name="inherit_id" ref="stock.view_location_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='scrap_location']" position='after'>
                <field name="is_subcontracting_location" groups="base.group_no_one" invisible="usage != 'internal'"/>
            </xpath>
        </field>
    </record>
</odoo>
