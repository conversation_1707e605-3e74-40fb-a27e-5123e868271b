# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
#
# Translators:
# <PERSON><PERSON><PERSON> <dj<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-12 10:37+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$1000"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$5000"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "2023-08-15"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Analytic Account</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span style=\"margin-right: 15px;\">Total</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Amount</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Date</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Product</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Quantity</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Ref.</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Unit of Measure</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Acme Corp."
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_account_ids
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_account_ids
msgid "Analytic Account"
msgstr "Račun analitike"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
msgid "Analytic Accounts"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_distribution
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_distribution
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution
msgid "Analytic Distribution"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_distribution_text
msgid "Analytic Distribution Text"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linija analitike"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_precision
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_precision
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_bom
msgid "Bill of Material"
msgstr "Racun Materijala"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Bills of Materials"
msgstr "Troškovi materijala"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "BoM brojač"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
msgid "Bom"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Kategorija"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_ids
msgid "Costs Hour Account"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr "Stavka izveštaja"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Laptop"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_applicability__business_domain__manufacturing_order
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Proizvodni nalog"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Manufacturing Orders"
msgstr "Nalozi za proizvodnj"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
msgid "Manufacturing Orders Count"
msgstr "Brojač naloga za proizvodnju"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_ids
msgid "Mo Analytic Account Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr "Proizvod"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr "Varijante proizvoda"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
msgid "Production"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "Nalog za proizvodnju"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "REF123"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Premeštanje zaliha"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr "Pravilo zalihe"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and "
"final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will "
"remain on the account once the production is completed."
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Units"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "WIP Report for"
msgstr ""

#. module: mrp_account
#: model:ir.actions.report,name:mrp_account.wip_report
msgid "WIP report"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_ids
msgid "Wc Analytic Account Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "Radionica"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "Brojač radnih naloga"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
msgid "Work Orders"
msgstr "Radni Nalozi"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenter"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
msgid "[WC] %s"
msgstr ""
