# -*- coding: utf-8 -*-
from odoo import fields, models


class ProjectCategory(models.Model):
    _name = 'project.category'
    _description = '项目分类'
    _order = 'sequence, name'

    name = fields.Char('分类名称', required=True, translate=True)
    sequence = fields.Integer('序号', default=10)
    active = fields.<PERSON><PERSON>an('启用', default=True)
    description = fields.Text('描述')
    color = fields.Integer('颜色', default=0)

    _sql_constraints = [
        ('name_uniq', 'unique (name)', '分类名称必须唯一！'),
    ]
