<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_picking_form_view" model="ir.ui.view">
        <field name="name">stock.picking.form.view</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <xpath expr="//button[@name='button_validate'][hasclass('o_btn_validate')]" position="before">
                <field name="display_action_record_components" invisible="1"/>
                <button name="action_record_components" class="oe_highlight" invisible="display_action_record_components != 'mandatory'" string="Record components" type="object" data-hotkey="shift+x"/>
                <button name="action_record_components" invisible="display_action_record_components != 'facultative'" string="Record components" type="object" data-hotkey="shift+x"/>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']//list" position="inside">
                <field name="show_subcontracting_details_visible" column_invisible="True"/>
                <button name="action_show_subcontract_details" string="Subcontracting" type="object" icon="fa-pencil"
                    invisible="not show_subcontracting_details_visible"/>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']//list" position="inside">
                <field name="is_subcontract" column_invisible="True"/>
                <button name="action_show_details" invisible="not is_subcontract" type="object" icon="fa-list" width="0.1" title="Details"/>
            </xpath>
        </field>
    </record>
</odoo>
