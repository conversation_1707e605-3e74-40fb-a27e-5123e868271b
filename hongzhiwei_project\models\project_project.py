# -*- coding: utf-8 -*-
from odoo import api, fields, models


class ProjectProject(models.Model):
    _inherit = 'project.project'

    # 基础信息
    project_number = fields.Char('项目序号', required=True, copy=False, index=True)
    project_status = fields.Selection([
        ('draft', '草稿'),
        ('planning', '规划中'),
        ('in_progress', '进行中'),
        ('testing', '测试中'),
        ('delivered', '已交付'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ], string='项目状态', default='draft', required=True, tracking=True)
    
    # 人员信息
    delivery_person_id = fields.Char('项目交付人', tracking=True)
    hongzhiwei_contact_id = fields.Char('鸿之微对接人', tracking=True)
    project_category_id = fields.Char('项目分类', tracking=True)
    project_manager_id = fields.Many2one('res.users', string='项目负责人', tracking=True)
    team_member_ids = fields.Many2many('res.users', 'project_team_member_rel', 
                                       'project_id', 'user_id', string='项目团队成员')
    
    # 时间管理
    progress_update_time = fields.Datetime('总体进度更新时间', tracking=True)
    deadline = fields.Date('截止时间', tracking=True)
    next_progress_update = fields.Datetime('下次总体进度更新时间', tracking=True)
    
    # 工作量统计
    planned_person_days = fields.Char('计划投入人天', tracking=True, 
                                     help='例如：1人*200天（200）')
    consumed_person_days = fields.Char('已消耗总人天', tracking=True,
                                      help='例如：1人*100天（100）')
    
    # 项目描述
    overall_task_description = fields.Html('项目总体任务描述')
    time_nodes = fields.Text('时间节点')
    current_milestone = fields.Char('里程碑（当前）', tracking=True)
    next_milestone_time = fields.Datetime('下次里程碑时间', tracking=True)
    project_notes = fields.Text('备注')
    
    # 计算字段 - 从文本中提取数值用于进度计算
    planned_days_numeric = fields.Float('计划人天数值', compute='_compute_numeric_days', store=True)
    consumed_days_numeric = fields.Float('消耗人天数值', compute='_compute_numeric_days', store=True)

    @api.depends('planned_person_days', 'consumed_person_days')
    def _compute_numeric_days(self):
        """从文本字段提取数值用于计算"""
        for project in self:
            project.planned_days_numeric = self._extract_numeric_value(project.planned_person_days)
            project.consumed_days_numeric = self._extract_numeric_value(project.consumed_person_days)

    def _extract_numeric_value(self, text_value):
        """从文本中提取数值"""
        if not text_value:
            return 0.0
        
        import re
        text = str(text_value).strip()
        
        # 提取括号内的数字：1人*200天（200）
        pattern1 = r'[（(](\d+\.?\d*)[）)]'
        match1 = re.search(pattern1, text)
        if match1:
            return float(match1.group(1))
        
        # 提取乘法结果：1人*200天
        pattern2 = r'(\d+)人\*(\d+)天'
        match2 = re.search(pattern2, text)
        if match2:
            return float(int(match2.group(1)) * int(match2.group(2)))
        
        # 提取纯数字
        numbers = re.findall(r'\d+\.?\d*', text)
        if numbers:
            return float(numbers[-1])
        
        return 0.0

    @api.model
    def create(self, vals):
        if not vals.get('project_number'):
            vals['project_number'] = self.env['ir.sequence'].next_by_code('project.number') or '/'
        return super().create(vals)
