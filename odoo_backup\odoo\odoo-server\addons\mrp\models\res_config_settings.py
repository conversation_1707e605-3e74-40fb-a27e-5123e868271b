# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    manufacturing_lead = fields.Float(related='company_id.manufacturing_lead', string="Manufacturing Lead Time", readonly=False)
    use_manufacturing_lead = fields.Boolean(string="Default Manufacturing Lead Time", config_parameter='mrp.use_manufacturing_lead')
    group_mrp_byproducts = fields.<PERSON><PERSON>an("By-Products",
        implied_group='mrp.group_mrp_byproducts')
    module_mrp_mps = fields.<PERSON><PERSON>an("Master Production Schedule")
    module_mrp_plm = fields.<PERSON><PERSON><PERSON>("Product Lifecycle Management (PLM)")
    module_quality_control = fields.Boolean("Quality")
    module_quality_control_worksheet = fields.Boolean("Quality Worksheet")
    module_mrp_subcontracting = fields.<PERSON>olean("Subcontracting")
    group_mrp_routings = fields.<PERSON><PERSON><PERSON>("MRP Work Orders",
        implied_group='mrp.group_mrp_routings')
    group_unlocked_by_default = fields.<PERSON><PERSON><PERSON>("Unlock Manufacturing Orders", implied_group='mrp.group_unlocked_by_default')
    group_mrp_reception_report = fields.Boolean("Allocation Report for Manufacturing Orders", implied_group='mrp.group_mrp_reception_report')
    group_mrp_workorder_dependencies = fields.Boolean("Work Order Dependencies", implied_group="mrp.group_mrp_workorder_dependencies")

    def set_values(self):
        routing_before = self.env.user.has_group('mrp.group_mrp_routings')
        super().set_values()
        if routing_before and not self.group_mrp_routings:
            self.env['mrp.routing.workcenter'].search([]).active = False
        elif not routing_before and self.group_mrp_routings:
            operations = self.env['mrp.routing.workcenter'].search_read([('active', '=', False)], ['id', 'write_date'])
            last_updated = max((op['write_date'] for op in operations), default=0)
            if last_updated:
                op_to_update = self.env['mrp.routing.workcenter'].browse([op['id'] for op in operations if op['write_date'] == last_updated])
                op_to_update.active = True
        if not self.group_mrp_workorder_dependencies:
            # Disabling this option should not interfere with currently planned productions
            self.env['mrp.bom'].sudo().search([('allow_operation_dependencies', '=', True)]).allow_operation_dependencies = False

    @api.onchange('use_manufacturing_lead')
    def _onchange_use_manufacturing_lead(self):
        if not self.use_manufacturing_lead:
            self.manufacturing_lead = 0.0

    @api.onchange('group_unlocked_by_default')
    def _onchange_group_unlocked_by_default(self):
        """ When changing this setting, we want existing MOs to automatically update to match setting. """
        if self.group_unlocked_by_default:
            self.env['mrp.production'].search([('state', 'not in', ('cancel', 'done')), ('is_locked', '=', True)]).is_locked = False
        else:
            self.env['mrp.production'].search([('state', 'not in', ('cancel', 'done')), ('is_locked', '=', False)]).is_locked = True
