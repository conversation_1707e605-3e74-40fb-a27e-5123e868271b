<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="label_production_view">
            <t t-set="uom_categ_unit" t-value="env.ref('uom.product_uom_categ_unit')"/>
            <t t-foreach="docs" t-as="production">
                <t t-foreach="production.move_finished_ids" t-as="move">
                    <t t-foreach="move.move_line_ids" t-as="move_line">
                        <t t-if="move_line.product_uom_id.category_id == uom_categ_unit">
                            <t t-set="qty" t-value="int(move_line.quantity)"/>
                        </t>
                        <t t-else="">
                            <t t-set="qty" t-value="1"/>
                        </t>
                        <t t-foreach="range(qty)" t-as="item">
                            <t t-translation="off">
^XA
^FO100,50
^A0N,44,33^FD<t t-esc="move_line.product_id.display_name"/>^FS
<t t-if="move_line.product_id.tracking != 'none' and move_line.lot_id">
^FO100,100
^A0N,44,33^FDLN/SN: <t t-esc="move_line.lot_id.name"/>^FS
^FO100,150^BY3
^BCN,100,Y,N,N
^FD<t t-esc="move_line.lot_id.name"/>^FS
</t>
<t t-elif="move_line.product_id.tracking == 'none' and move_line.product_id.barcode">
^FO100,100^BY3
^BCN,100,Y,N,N
^FD<t t-esc="move_line.product_id.barcode"/>^FS
</t>
<t t-else="">
^FO100,100
^A0N,44,33^FDNo barcode available^FS
</t>
^XZ
                            </t>
                        </t>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>
