<?xml version="1.0" encoding="utf-8"?>
<templates id="template">
    <t t-name="mrp.ForecastedDetails" t-inherit="stock.ForecastedDetails" t-inherit-mode="extension">
        <xpath expr="//tr[@name='draft_picking_in']" position="after">
            <tr t-if="props.docs.draft_production_qty.in" name="draft_mo_in">
                <td colspan="2">Production of Draft MO</td>
                <td t-out="_formatFloat(props.docs.draft_production_qty.in)" class="text-end"/>
            </tr>
        </xpath>
        <xpath expr="//tr[@name='draft_picking_out']" position="after">
            <tr t-if="props.docs.draft_production_qty.out" name="draft_mo_out">
                <td colspan="2">Component of Draft MO</td>
                <td t-out="_formatFloat(-props.docs.draft_production_qty.out)" class="text-end"/>
            </tr>
        </xpath>
        <xpath expr="//button[@name='change_priority_link']" position="after">
            <button t-if="line.move_out and line.move_out.raw_material_production_id"
                t-attf-class="o_priority o_priority_star fa fa-star#{line.move_out.raw_material_production_id.priority=='1' ? ' one' : '-o zero'}"
                t-on-click="() => this._onClickChangePriority('mrp.production', line.move_out.raw_material_production_id)"/>
        </xpath>
    </t>
</templates>
