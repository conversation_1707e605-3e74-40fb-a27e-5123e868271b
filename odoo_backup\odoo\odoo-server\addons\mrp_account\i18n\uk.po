# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$1000"
msgstr "$1000"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$5000"
msgstr "$5000"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
msgid "%s - Labour"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "2023-08-15"
msgstr "2023-08-15"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_move_form_inherit_mrp_account
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Виробництво</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">Вартість</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span style=\"margin-right: 15px;\">Total</span>"
msgstr "<span style=\"margin-right: 15px;\">Всього</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Amount</span>"
msgstr "<span>Сума</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Date</span>"
msgstr "<span>Дата</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Product</span>"
msgstr "<span>Товар</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Quantity</span>"
msgstr "<span>Кількість</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Ref.</span>"
msgstr "<span>Реф.</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Unit of Measure</span>"
msgstr "<span>Одиниця вимірювання</span>"

#. module: mrp_account
#: model:ir.model.constraint,message:mrp_account.constraint_mrp_account_wip_accounting_line_check_debit_credit
msgid "A single line cannot be both credit and debit."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__account_id
msgid "Account"
msgstr "Рахунок"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__account_move_line_id
msgid "Account Move Line"
msgstr "Рядок проведення"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting_line
msgid "Account move line to be created when posting WIP account move"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Acme Corp."
msgstr "Acme Corp."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "Аналітичний рахунок"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution
msgid "Analytic Distribution"
msgstr "Аналітичний розподіл"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Рядок аналітики"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Застосовуваність аналітичного плану"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_precision
msgid "Analytic Precision"
msgstr "Точність аналітики"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Bills of Materials"
msgstr "Специфікації"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "Підрахунок специфікацій"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
msgid "Bom"
msgstr "Специфікація"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Категорія"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "Розрахуйте ціну зі специфікації"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Обчисліть ціну товару, використовуючи товари та операції відповідної "
"специфікації, тільки для виготовлених товарів."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_ids
msgid "Costs Hour Account"
msgstr "Рахунок вартості години"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_uid
msgid "Created by"
msgstr "Створив"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_date
msgid "Created on"
msgstr "Створено"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__credit
msgid "Credit"
msgstr "Кредит"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__date
msgid "Date"
msgstr "Дата"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__debit
msgid "Debit"
msgstr "Дебет"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Discard"
msgstr "Відмінити"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Аналітичний рахунок розподілу"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Домен"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__expense_account_id
msgid "Expense Account"
msgstr "Рахунок витрат"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr "Вартість додаткової одиниці"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__id
msgid "ID"
msgstr "ID"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__journal_id
msgid "Journal"
msgstr "Журнал"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr "Елемент журналу"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__label
msgid "Label"
msgstr "Мітка"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Laptop"
msgstr "Ноутбук"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Звіт огляду MO"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manual Entry"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_applicability__business_domain__manufacturing_order
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Замовлення на виробництво"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Manufacturing Orders"
msgstr "Замовлення на виробництво"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_count
msgid "Manufacturing Orders Count"
msgstr "Підрахунок замовлень на виробництво"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manufacturing WIP - %(orders_list)s"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__mo_ids
msgid "Mo"
msgstr "Пн"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_ids
msgid "Mo Analytic Account Line"
msgstr "Рядок аналітичного рахунку Mo"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid ""
"Please make sure the total credit amount equals the total debit amount."
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post Manufacturing WIP"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post WIP"
msgstr ""

#. module: mrp_account
#: model:ir.actions.act_window,name:mrp_account.action_wip_accounting
msgid "Post WIP Accounting Entry"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr "Товар"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr "Категорія товару"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr "Варіант товару"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
msgid "Production"
msgstr "Виробництво"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr "Рахунок виробництва"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "REF123"
msgstr "REF123"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reference
msgid "Reference"
msgstr "Референс"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_ids
msgid "Relevant WIP MOs"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reversal_date
msgid "Reversal Date"
msgstr "Дата сторнування"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal date must be after the posting date."
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal of: %s"
msgstr "Сторнування: %s"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "Показати оцінку"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Складське переміщення "

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "Етап оцінювання запасу"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,help:mrp_account.field_account_move__wip_production_ids
msgid ""
"The MOs that this WIP entry was based on. Expected to be set at time of WIP "
"entry creation."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__expense_account_id
msgid ""
"The expense is accounted for when the manufacturing order is marked as done."
" If not set, it is the expense account of the final product that will be "
"used instead."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will remain on the account once the production is completed."
msgstr ""
"Цей рахунок використовуватиметься як відповідний для вартості компонентів і кінцевих товарів замовлень на виробництво.\n"
"                 Якщо є витрати на робочий центр/працівників, це значення залишиться на рахунку після завершення виробництва."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Credit"
msgstr "Всього кредит"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Debit"
msgstr "Всього дебет"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Units"
msgstr "Одиниці"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Component Value"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Overhead"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/account_move.py:0
msgid "WIP MOs of %s"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "WIP Report for"
msgstr "Звіт WIP для"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__line_ids
msgid "WIP accounting lines"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__wip_accounting_id
msgid "WIP accounting wizard"
msgstr ""

#. module: mrp_account
#: model:ir.actions.report,name:mrp_account.wip_report
msgid "WIP report"
msgstr "Звіт WIP"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_ids
msgid "Wc Analytic Account Line"
msgstr "Рядко аналітичного рахунку Wc"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting
msgid "Wizard to post Manufacturing WIP account move"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "Робочий центр"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Використання робочого центру"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "Робоче замовлення"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "Підрахунок робочих завдань"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
msgid "Work Orders"
msgstr "Робочі замовлення"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenter"
msgstr "Робочий центр"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Журнал продуктивності робочого центру"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
msgid "[WC] %s"
msgstr "[WC] %s"
