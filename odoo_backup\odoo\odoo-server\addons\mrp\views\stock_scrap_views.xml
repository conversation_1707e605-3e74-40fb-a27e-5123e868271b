<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_scrap_view_form2_mrp_inherit_mrp" model="ir.ui.view">
        <field name="name">stock.scrap.view.form2.inherit.mrp</field>
        <field name="model">stock.scrap</field>
        <field name="inherit_id" ref="stock.stock_scrap_form_view2"/>
        <field name="arch" type="xml">
            <field name="owner_id" position="after">
                <field name="workorder_id" invisible="1" readonly="state == 'done'"/>
                <field name="production_id" invisible="1" readonly="state == 'done'"/>
                <field name="product_is_kit" invisible="1"/>
                <field name="product_template" invisible="1"/>
                <field name="bom_id" invisible="not product_is_kit" readonly="state == 'done'" required="product_is_kit"/>
            </field>
        </field>
    </record>
    <record id="stock_scrap_view_form_mrp_inherit_mrp" model="ir.ui.view">
        <field name="name">stock.scrap.view.form.inherit.mrp</field>
        <field name="model">stock.scrap</field>
        <field name="inherit_id" ref="stock.stock_scrap_form_view"/>
        <field name="arch" type="xml">
            <field name="owner_id" position="after">
                <field name="workorder_id" domain="[('production_id', '=', product_id)]" invisible="not workorder_id" readonly="state == 'done'"/>
                <field name="production_id" domain="[('company_id', '=', company_id)]" invisible="not production_id" readonly="state == 'done'"/>
                <field name="product_is_kit" invisible="1"/>
                <field name="product_template" invisible="1"/>
                <field name="bom_id" invisible="not product_is_kit" readonly="state == 'done'" required="product_is_kit"/>
            </field>
        </field>
    </record>

    <record id="stock_scrap_search_view_inherit_mrp" model="ir.ui.view">
        <field name="name">stock.scrap.search.inherit.mrp</field>
        <field name="model">stock.scrap</field>
        <field name="inherit_id" ref="stock.stock_scrap_search_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='create_date']" position="after">
                <filter string="Draft" name="filter_draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Done" name="filter_done" domain="[('state', '=', 'done')]"/>
            </xpath>
            <xpath expr="//filter[@name='transfer']" position="after">
                <filter string="Manufacturing Order" name="production_id" domain="[]" context="{'group_by':'production_id'}"/>
            </xpath>
        </field>
    </record>
</odoo>
