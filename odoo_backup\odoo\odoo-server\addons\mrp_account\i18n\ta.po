# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_extended
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2016-02-05 09:53+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Analytic Account</span>"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: model:ir.model,name:mrp_account.model_account_analytic_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid "Analytic Account"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__analytic_account_id
msgid "Analytic account in which cost and revenue entries will take        place for financial management of the manufacturing order."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_bom__analytic_account_id
msgid "Analytic account in which cost and revenue entries will take place for financial management of the manufacturing order."
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_bom
msgid "Bill of Material"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Bills of Materials"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr ""

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute the price of the product using products and operations of related bill of materials, for manufactured products only."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Manufacturing Orders"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
msgid "Manufacturing Orders Count"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_id
msgid "Mo Analytic Account Line"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid "Posts analytical accounting entries in real time for both component and operational costs."
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will remain on the account once the production is completed."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_id
msgid "Wc Analytic Account Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
msgid "Work Orders"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenters"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/stock_move.py:0
msgid "[Raw] %s"
msgstr ""

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
msgid "[WC] %s"
msgstr ""
