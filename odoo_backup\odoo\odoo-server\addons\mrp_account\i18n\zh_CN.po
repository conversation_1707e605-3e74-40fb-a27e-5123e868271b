# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$1000"
msgstr "$1000"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$5000"
msgstr "$5000"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
msgid "%s - Labour"
msgstr "%s - 劳动力"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "2023-08-15"
msgstr "2023 年 8 月 15 日"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_move_form_inherit_mrp_account
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">制造</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">估值</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span style=\"margin-right: 15px;\">Total</span>"
msgstr "<span style=\"margin-right: 15px;\">总计</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Amount</span>"
msgstr "<span>金额</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Date</span>"
msgstr "<span>日期</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Product</span>"
msgstr "<span>产品</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Quantity</span>"
msgstr "<span>数量</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Ref.</span>"
msgstr "<span>参考</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Unit of Measure</span>"
msgstr "<span>计量单位</span>"

#. module: mrp_account
#: model:ir.model.constraint,message:mrp_account.constraint_mrp_account_wip_accounting_line_check_debit_credit
msgid "A single line cannot be both credit and debit."
msgstr "借方和贷方不能同时在一行之中"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__account_id
msgid "Account"
msgstr "账户"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__account_move_line_id
msgid "Account Move Line"
msgstr "会计分录资料行"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting_line
msgid "Account move line to be created when posting WIP account move"
msgstr "在过账在制品账户分录时要创建的账户分录"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Acme Corp."
msgstr "Acme Corp."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "分析账户"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution
msgid "Analytic Distribution"
msgstr "分析分摊"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析行"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "分析方案的适用性"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_precision
msgid "Analytic Precision"
msgstr "分析精度"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Bills of Materials"
msgstr "物料清单"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "BoM 计数"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
msgid "Bom"
msgstr "BoM"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "类别"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "从物料清单（BOM）计算价格"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr "使用物料清单的产品和作业计算价格，仅限已制造产品。"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_ids
msgid "Costs Hour Account"
msgstr "成本 小时 账户"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_uid
msgid "Created by"
msgstr "创建人"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_date
msgid "Created on"
msgstr "创建日期"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__credit
msgid "Credit"
msgstr "贷方"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__currency_id
msgid "Currency"
msgstr "币别"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__date
msgid "Date"
msgstr "日期"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__debit
msgid "Debit"
msgstr "借方"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Discard"
msgstr "丢弃"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "分析分布账户"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "域"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__expense_account_id
msgid "Expense Account"
msgstr "费用科目"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr "额外单位成本"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__id
msgid "ID"
msgstr "ID"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__journal_id
msgid "Journal"
msgstr "日记账"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move
msgid "Journal Entry"
msgstr "日记账分录"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr "日记账项目"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__label
msgid "Label"
msgstr "标签"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Laptop"
msgstr "笔记本电脑"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "MO 概览报表"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manual Entry"
msgstr "手工分录"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_applicability__business_domain__manufacturing_order
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "制造订单"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Manufacturing Orders"
msgstr "制造订单"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_count
msgid "Manufacturing Orders Count"
msgstr "制造订单计数"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manufacturing WIP - %(orders_list)s"
msgstr "制造中的产品 - %(orders_list)s"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__mo_ids
msgid "Mo"
msgstr "Mo"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_ids
msgid "Mo Analytic Account Line"
msgstr "制造订单（MO）分析科目行"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid ""
"Please make sure the total credit amount equals the total debit amount."
msgstr "请确保总借方金额等于总贷方金额。"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post Manufacturing WIP"
msgstr "发布制造中的在制品"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post WIP"
msgstr "发布在制品"

#. module: mrp_account
#: model:ir.actions.act_window,name:mrp_account.action_wip_accounting
msgid "Post WIP Accounting Entry"
msgstr "发布 WIP 会计凭证"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr "产品"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr "产品类别"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr "产品变体"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
msgid "Production"
msgstr "生产"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr "生产科目"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "REF123"
msgstr "REF123"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reference
msgid "Reference"
msgstr "参考号"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_ids
msgid "Relevant WIP MOs"
msgstr "相关制造订单在制品"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reversal_date
msgid "Reversal Date"
msgstr "冲销日期"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal date must be after the posting date."
msgstr "冲销日期必须在过账日期之后"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal of: %s"
msgstr "撤销：%s"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "显示估值"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "库存移动"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "库存估值层"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,help:mrp_account.field_account_move__wip_production_ids
msgid ""
"The MOs that this WIP entry was based on. Expected to be set at time of WIP "
"entry creation."
msgstr "此半成品基于制造订单。预计在创建半成品项时设置。"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__expense_account_id
msgid ""
"The expense is accounted for when the manufacturing order is marked as done."
" If not set, it is the expense account of the final product that will be "
"used instead."
msgstr "当制造订单标记为完成时，将记入费用。如果未设置，则将使用最终产品的费用账户。"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will remain on the account once the production is completed."
msgstr ""
"该账户将用作制造订单的组件和最终产品的估值对应账户。\n"
"                如果有任何工作中心/员工成本，制造完成后，该值将保留在账户中。"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Credit"
msgstr "贷方合计"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Debit"
msgstr "借方合计"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Units"
msgstr "单位"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Component Value"
msgstr "WIP - 组件值"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Overhead"
msgstr "WIP - 顶部"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/account_move.py:0
msgid "WIP MOs of %s"
msgstr "WIP MOs of %s"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "WIP Report for"
msgstr "WIP 报告"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__line_ids
msgid "WIP accounting lines"
msgstr "半成品会计资料行"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__wip_accounting_id
msgid "WIP accounting wizard"
msgstr "半成品会计向导"

#. module: mrp_account
#: model:ir.actions.report,name:mrp_account.wip_report
msgid "WIP report"
msgstr "WIP 报告"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_ids
msgid "Wc Analytic Account Line"
msgstr "工作中心（WC）分析科目行"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting
msgid "Wizard to post Manufacturing WIP account move"
msgstr "发布制造半成品会计分录向导"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "工作中心"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "工作中心使用情况"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "工单"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "工单计数"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
msgid "Work Orders"
msgstr "工单"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenter"
msgstr "工作中心"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "工作中心生产力日志"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
msgid "[WC] %s"
msgstr "[WC] %s"
