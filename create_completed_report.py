import pandas as pd

# 读取原始Excel文件
df = pd.read_excel('下载进度.xlsx')

print('=== 筛选已完成项目 ===')

# 筛选已完成的项目（包括'已完成'和'完成'状态）
completed_df = df[df['状态'].isin(['已完成', '完成'])].copy()

print(f'已完成项目总数: {len(completed_df)}')

# 分别统计两种完成状态
completed_count = len(df[df['状态'] == '已完成'])
finished_count = len(df[df['状态'] == '完成'])

print(f'其中"已完成"状态: {completed_count}')
print(f'其中"完成"状态: {finished_count}')

# 选择需要的列
columns_to_keep = ['编号', '资源名', '状态', '下载地址', '总文件数', '缺失文件数']
completed_summary = completed_df[columns_to_keep].copy()

# 添加一个已完成标记列
completed_summary['已完成'] = '是'

# 重新排列列的顺序
final_columns = ['已完成', '编号', '资源名', '状态', '下载地址', '总文件数', '缺失文件数']
completed_summary = completed_summary[final_columns]

# 保存到新的Excel文件
output_file = '已完成项目统计.xlsx'
completed_summary.to_excel(output_file, index=False)

print(f'\n已完成项目统计表已保存到: {output_file}')
print(f'包含 {len(completed_summary)} 条已完成记录')

# 显示前10条记录作为预览
print('\n=== 已完成项目预览（前10条）===')
if len(completed_summary) > 0:
    print(completed_summary.head(10))

# 按下载地址统计已完成数量
print('\n=== 按下载地址统计已完成数量 ===')
if len(completed_summary) > 0:
    address_stats = completed_summary['下载地址'].value_counts(dropna=False)
    for addr, count in address_stats.items():
        print(f'{addr}: {count}项')

# 按状态统计
print('\n=== 按状态统计 ===')
if len(completed_summary) > 0:
    status_stats = completed_summary['状态'].value_counts()
    for status, count in status_stats.items():
        print(f'{status}: {count}项')

print('\n=== 统计完成 ===')
