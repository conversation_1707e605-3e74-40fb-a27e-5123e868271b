# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_repair
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# emre oktem, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mrp_repair
#: model:ir.model.fields,field_description:mrp_repair.field_repair_order__production_count
msgid "Count of MOs generated"
msgstr ""

#. module: mrp_repair
#: model:ir.model.fields,field_description:mrp_repair.field_mrp_production__repair_count
msgid "Count of source repairs"
msgstr "Kaynak onarım sayısı"

#. module: mrp_repair
#: model_terms:ir.ui.view,arch_db:mrp_repair.view_repair_order_form_inherit
msgid "Manufacturing"
msgstr "Üretim"

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_mrp_production
msgid "Manufacturing Order"
msgstr "Üretim Emri"

#. module: mrp_repair
#. odoo-python
#: code:addons/mrp_repair/models/repair.py:0
msgid "Manufacturing Orders generated by %s"
msgstr ""

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_repair_order
msgid "Repair Order"
msgstr "Onarım Siparişi"

#. module: mrp_repair
#. odoo-python
#: code:addons/mrp_repair/models/production.py:0
msgid "Repair Source of %s"
msgstr "%s Onarım Kaynağı"

#. module: mrp_repair
#: model_terms:ir.ui.view,arch_db:mrp_repair.mrp_production_form_view_inherit
msgid "Repairs"
msgstr "Onarımlar"

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_stock_move
msgid "Stock Move"
msgstr "Stok Hareketi"
