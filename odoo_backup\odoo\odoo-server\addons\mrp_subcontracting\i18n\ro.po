# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Larisa Pop, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
msgid "%(name)s Sequence Resupply Subcontractor"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
msgid "%(name)s Sequence subcontracting"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal
msgid "/my/productions"
msgstr "/my/productions"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "All"
msgstr "Tot"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Raport general LDM"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_bom
msgid "Bill of Material"
msgstr "Listă de materiale"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__type
msgid "BoM Type"
msgstr "Tip LdM"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__bom_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__bom_ids
msgid "BoMs for which the Partner is one of the subcontractors"
msgstr "LdM pentru care partenerul este unul dintre subcontractanți"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid "Bom Product"
msgstr "LDM Produs"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_change_production_qty
msgid "Change Production Qty"
msgstr "Schimbați cantitatea de producție"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid ""
"Check this box to create a new dedicated subcontracting location for this "
"company. Note that standard subcontracting routes will be adapted so as to "
"take these into account automatically."
msgstr ""
"Bifați această casetă pentru a crea o nouă locație de subcontractare "
"dedicată pentru această companie. Rețineți că rutele standard de "
"subcontractare vor fi adaptate pentru a le lua în considerare automat."

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid ""
"Choose a vendor of type subcontractor if you want to subcontract the product"
msgstr ""
"Alegeți un furnizor de tip subcontractor dacă doriți să subcontractați "
"produsul"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Consumed"
msgstr "Consumat"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_partner
msgid "Contact"
msgstr "Contactați"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Continue"
msgstr "Continuați"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Deadline Date"
msgstr "Dată limită"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Demand"
msgstr "Necesar"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Description"
msgstr "Descriere"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__move_line_raw_ids
msgid "Detail Component"
msgstr "Componenta detaliu"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Details"
msgstr "Detalii"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Discard"
msgstr "Abandonează"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_action_record_components
msgid "Display Action Record Components"
msgstr "Afișați componentele înregistrării acțiunii"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Done"
msgstr "Efectuat"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__facultative
msgid "Facultative"
msgstr "Facultativ"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_productions
msgid "Follow manufacturing orders you have to fulfill"
msgstr "Urmăriți comenzile de producție pe care trebuie să le îndepliniți"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontracting_has_been_recorded
msgid "Has been recorded?"
msgstr "A fost înregistrat?"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__hide
msgid "Hide"
msgstr "Ascunde"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
msgid ""
"In order to manage stock accurately, subcontracting locations must be type "
"Internal, linked to the appropriate company."
msgstr ""
"Pentru a gestiona stocul cu precizie, locațiile de subcontractare trebuie să"
" fie de tip intern, legate de compania corespunzătoare."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_filter
msgid "Incoming transfer"
msgstr "Transfer de intrare"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_location
msgid "Inventory Locations"
msgstr "Locații inventar"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_quant__is_subcontract
msgid "Is Subcontract"
msgstr "Este subcontract"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid "Is a Subcontracting Location?"
msgstr "Este o locație de subcontractare?"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid ""
"List of Products used in the BoM, used to filter the list of products in the"
" subcontracting portal view"
msgstr ""
"Listă de produse utilizate în LdM, folosită pentru a filtra lista de produse"
" în vizualizarea portalului de subcontractare"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__production_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__production_ids
msgid "MRP Productions for which the Partner is the subcontractor"
msgstr "Producții MRP pentru care partenerul este subcontractant"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move_line.py:0
msgid ""
"Make sure you validate or adapt the related resupply picking to your "
"subcontractor in order to avoid inconsistencies in your stock."
msgstr ""
"Asigurați-vă că validați sau adaptați ridicarea de reaprovizionare "
"corespunzătoare subcontractantului dvs. pentru a evita inconsistentele în "
"stocul dvs."

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__mandatory
msgid "Mandatory"
msgstr "Obligatoriu"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_production
msgid "Manufacturing Order"
msgstr "Comanda de Producție"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_productions
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Orders"
msgstr "Comandă de producție"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Reference"
msgstr "Referință producție"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "Name"
msgstr "Nume"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "Newest"
msgstr "Cele mai noi"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
msgid "Nothing to record"
msgstr "Nimic de înregistrat"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_quant.py:0
msgid "Operation not supported"
msgstr "Operațiunea nu este acceptată"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Operations"
msgstr "Operații"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Operații fără ambalaj"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Order"
msgstr "Comandă"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
msgid ""
"Portal users cannot create a stock move with a state 'Done' or change the "
"current state to 'Done'."
msgstr ""
"Utilizatorii portalului nu pot crea o mișcare de stoc cu o stare „Efectuată”"
" sau schimba starea curentă în „Efectuată”."

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mișcări de produs (linie mișcare de stoc)"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_product
msgid "Product Variant"
msgstr "Variantă de produs"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_menu_production
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Productions"
msgstr "Productii"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_quant
msgid "Quants"
msgstr "Poziții de stoc"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
msgid "Raw Materials for %s"
msgstr "Materii prime pentru %s"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
msgid "Ready"
msgstr "Pregătit"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Record Production"
msgstr "Înregistrare producție"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Record components"
msgstr "Înregistrați componentele"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Register components for subcontracted product"
msgstr "Înregistrați componentele pentru produsul subcontractat"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr "Reaprovizionare la comandă (MTO)"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_route_id
msgid "Resupply Subcontractor"
msgstr "Reaprovizionare Subcontractor"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_replenish_mixin.py:0
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:stock.route,name:mrp_subcontracting.route_resupply_subcontractor_mto
msgid "Resupply Subcontractor on Order"
msgstr "Furnizați subcontractorul la comandă"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply Subcontractors"
msgstr "Reaprovizionare subcontractori"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking
msgid "Return Picking"
msgstr "Retur livrare"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Linie Ridicare Retur"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Scheduled Date"
msgstr "Dată planificată"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__show_subcontracting_details_visible
msgid "Show Subcontracting Details Visible"
msgstr "Afișați Detaliile de Subcontractare Vizibile"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Source Document"
msgstr "Document sursă"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "State"
msgstr "Județ"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move
msgid "Stock Move"
msgstr "Mișcare de stoc"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__picking_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__picking_ids
msgid "Stock Pickings for which the Partner is the subcontractor"
msgstr "Ridicările de stoc pentru care partenerul este subcontractant"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_rule
msgid "Stock Rule"
msgstr "Regulă stoc"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
msgid "Subcontract"
msgstr "Subcontract"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid "Subcontracted"
msgstr "Subcontractat"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "Subcontracted manufacturing orders cannot be merged."
msgstr "Comenzile de producție subcontractate nu pot fi combinate."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__mrp_bom__type__subcontract
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Subcontracting"
msgstr "Subcontractare"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/res_company.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__subcontracting_location_id
msgid "Subcontracting Location"
msgstr "Locație subcontractare"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.quant_subcontracting_search_view
msgid "Subcontracting Locations"
msgstr "Locații subcontractare"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_mto_pull_id
msgid "Subcontracting MTO Rule"
msgstr "Regulă subcontractare MTO"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_pull_id
msgid "Subcontracting MTS Rule"
msgstr "Regulă subcontractare MTS"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_type_id
msgid "Subcontracting Operation Type"
msgstr "Tip operație subcontractare"

#. module: mrp_subcontracting
#: model:ir.actions.act_window,name:mrp_subcontracting.subcontracting_portal_view_production_action
msgid "Subcontracting Portal"
msgstr "Portal subcontractare"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_resupply_type_id
msgid "Subcontracting Resupply Operation Type"
msgstr "Tip operație reaprovizionare subcontractare"

#. module: mrp_subcontracting
#. odoo-javascript
#: code:addons/mrp_subcontracting/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
msgid "Subcontracting:"
msgstr "Subcontractare:"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/report/mrp_report_bom_structure.py:0
msgid "Subcontracting: %s"
msgstr "Subcontractare: %s"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontractor_id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__subcontractor_ids
msgid "Subcontractor"
msgstr "Subcontractant"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid "Subcontractor Location"
msgstr "Locație subcontractant"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__subcontractor_ids
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.view_partner_mrp_subcontracting_filter
msgid "Subcontractors"
msgstr "Subcontractori"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Lista de preturi Furnizor"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__is_subcontract
msgid "The move is a subcontract receipt"
msgstr "Mișcarea este o primire subcontract"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,help:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid ""
"The stock location used as source and destination when sending        goods "
"to this contact during a subcontracting process."
msgstr ""
"Locația de stoc folosită ca sursă și destinație atunci când se trimit "
"mărfurile acestui contact în timpul unui proces de subcontractare."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "There are currently no productions for your account."
msgstr "În prezent nu există producții pentru contul dvs."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
msgid ""
"There shouldn't be multiple productions to record for the same subcontracted"
" move."
msgstr ""
"Nu ar trebui să existe mai multe producții de înregistrat pentru aceeași "
"mișcare subcontractată."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "This MO isn't related to a subcontracted move"
msgstr "Această CP nu este legată de o mișcare subcontractată"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_form_view
msgid "Total Consumed"
msgstr "Total consumat"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_picking
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__incoming_picking
msgid "Transfer"
msgstr "Transfer"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__subcontractor_id
msgid "Used to restrict access to the portal user through Record Rules"
msgstr ""
"Folosit pentru a restricționa accesul utilizatorului portalului prin reguli "
"de înregistrare"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_warehouse
msgid "Warehouse"
msgstr "Depozit"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"Asistent în caz de consum în avertizare/strict și mai multe componente au "
"fost utilizate pentru o CP (legată de LdM)"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_bom.py:0
msgid ""
"You can not set a Bill of Material with operations or by-product line as "
"subcontracting."
msgstr ""
"Nu puteți seta o listă de materiale cu operații sau linie de produs "
"secundară ca subcontractare."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
msgid "You cannot alter the company's subcontracting location"
msgstr "Nu puteți modifica locația de subcontractare a companiei"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "You cannot write on fields %s in mrp.production."
msgstr "Nu puteți scrie pe câmpurile %s în mrp.production."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "You must enter a serial number for %s"
msgstr "Trebuie să introduceți un număr de serie pentru %s"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid "You must enter a serial number for each line of %s"
msgstr "Trebuie să introduceți un număr de serie pentru fiecare linie a %s"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""
"Trebuie să indicați o cantitate consumată diferită de zero pentru cel puțin "
"unul dintre componentele dvs."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "e.g. PO0032"
msgstr "de exemplu PO0032"
