Metadata-Version: 2.1
Name: num2words
Version: 0.5.10
Summary: Modules to convert numbers to words. Easily extensible.
Home-page: https://github.com/savoirfairelinux/num2words
Author: <PERSON><PERSON> <tso at users sourceforge net>
Author-email: <EMAIL>
Maintainer: Savoir-faire Linux inc.
Maintainer-email: <EMAIL>
License: LGPL
Keywords: number word numbers words convert conversion i18n localisation localization internationalisation internationalization
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Library or Lesser General Public License (LGPL)
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Software Development :: Internationalization
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Localization
Classifier: Topic :: Text Processing :: Linguistic
Requires-Dist: docopt (>=0.6.2)

num2words - Convert numbers to words in multiple languages
==========================================================

.. image:: https://img.shields.io/pypi/v/num2words.svg
   :target: https://pypi.python.org/pypi/num2words

.. image:: https://travis-ci.org/savoirfairelinux/num2words.svg?branch=master
    :target: https://travis-ci.org/savoirfairelinux/num2words

.. image:: https://coveralls.io/repos/github/savoirfairelinux/num2words/badge.svg?branch=master
    :target: https://coveralls.io/github/savoirfairelinux/num2words?branch=master


``num2words`` is a library that converts numbers like ``42`` to words like ``forty-two``.
It supports multiple languages (see the list below for full list
of languages) and can even generate ordinal numbers like ``forty-second``
(although this last feature is a bit buggy for some languages at the moment).

The project is hosted on GitHub_. Contributions are welcome.

.. _GitHub: https://github.com/savoirfairelinux/num2words

Installation
------------

The easiest way to install ``num2words`` is to use pip::

    pip install num2words

Otherwise, you can download the source package and then execute::

    python setup.py install

The test suite in this library is new, so it's rather thin, but it can be run with::

    python setup.py test

To run the full CI test suite which includes linting and multiple python environments::

    pip install tox
    tox

Usage
-----
Command line::

    $ num2words 10001
    ten thousand and one
    $ num2words 24,120.10
    twenty-four thousand, one hundred and twenty point one
    $ num2words 24,120.10 -l es
    veinticuatro mil ciento veinte punto uno
    $num2words 2.14 -l es --to currency
    dos euros con catorce centimos

In code there's only one function to use::

    >>> from num2words import num2words
    >>> num2words(42)
    forty-two
    >>> num2words(42, to='ordinal')
    forty-second
    >>> num2words(42, lang='fr')
    quarante-deux

Besides the numerical argument, there are two main optional arguments.

**to:** The converter to use. Supported values are:

* ``cardinal`` (default)
* ``ordinal``
* ``ordinal_num``
* ``year``
* ``currency``

**lang:** The language in which to convert the number. Supported values are:

* ``en`` (English, default)
* ``ar`` (Arabic)
* ``cz`` (Czech)
* ``de`` (German)
* ``dk`` (Danish)
* ``en_GB`` (English - Great Britain)
* ``en_IN`` (English - India)
* ``es`` (Spanish)
* ``es_CO`` (Spanish - Colombia)
* ``es_VE`` (Spanish - Venezuela)
* ``eu`` (EURO)
* ``fi`` (Finnish)
* ``fr`` (French)
* ``fr_CH`` (French - Switzerland)
* ``fr_BE`` (French - Belgium)
* ``fr_DZ`` (French - Algeria)
* ``he`` (Hebrew)
* ``id`` (Indonesian)
* ``it`` (Italian)
* ``ja`` (Japanese)
* ``kn`` (Kannada)
* ``ko`` (Korean)
* ``lt`` (Lithuanian)
* ``lv`` (Latvian)
* ``no`` (Norwegian)
* ``pl`` (Polish)
* ``pt`` (Portuguese)
* ``pt_BR`` (Portuguese - Brazilian)
* ``sl`` (Slovene)
* ``sr`` (Serbian)
* ``ro`` (Romanian)
* ``ru`` (Russian)
* ``sl`` (Slovene)
* ``tr`` (Turkish)
* ``th`` (Thai)
* ``vi`` (Vietnamese)
* ``nl`` (Dutch)
* ``uk`` (Ukrainian)

You can supply values like ``fr_FR``; if the country doesn't exist but the
language does, the code will fall back to the base language (i.e. ``fr``). If
you supply an unsupported language, ``NotImplementedError`` is raised.
Therefore, if you want to call ``num2words`` with a fallback, you can do::

    try:
        return num2words(42, lang=mylang)
    except NotImplementedError:
        return num2words(42, lang='en')

Additionally, some converters and languages support other optional arguments
that are needed to make the converter useful in practice.

Wiki
----
For additional information on some localization please check the Wiki_.
And feel free to propose wiki enhancement.

.. _Wiki: https://github.com/savoirfairelinux/num2words/wiki

History
-------

``num2words`` is based on an old library, ``pynum2word``, created by Taro Ogawa
in 2003. Unfortunately, the library stopped being maintained and the author
can't be reached. There was another developer, Marius Grigaitis, who in 2011
added Lithuanian support, but didn't take over maintenance of the project.

I am thus basing myself on Marius Grigaitis' improvements and re-publishing
``pynum2word`` as ``num2words``.

Virgil Dupras, Savoir-faire Linux


Changelog
=========


Version 0.5.10 -- 2019/05/12
----------------------------

* Add Kannada language localization (#243)
* Revert some copyrights changed by mistake (#254)
* Add indian rupee to the supported currencies (#248)
* Improve currency functions for German and French (#247)
* Improve Slovene localization (#246)
* Improve Spanish localization (#240)
* Fix typo 'seperator' on source code (#238)
* Convert string to decimal values (#223)
* Improve German localization and test coverage (#237)
* Improve Polish localization (#233)
* Fix ordinal number for French ending on 1 (#236)

Version 0.5.9 -- 2019/01/10
---------------------------

* Fix encoding issue on release 0.5.8 (#229)
* Improve Polish localization (#228)


Version 0.5.8 -- 2018/11/17
---------------------------

* Add Portuguese (Portugal) localization (#198)
* Add a command line tool to use num2words
* Use language iso code for Vietnamese
* Improve Korean localization (#219)
* Improve Serbian (Latin) localization (#207)
* Improve testing setup (#220)
* Improve German localization (#214) (#222)
* Improve Romanian localization (#215)
* Improve Spanish localization (#187) (#200)
* Improve Russian localization (#211) (#212)
* Improve French localization (23902ab)
* Improve Arabic localization (#176)
* Improve Lithuanian and Latvian localization (#185)
* Improve Ukrainian localization (#183)


Version 0.5.7 -- 2018/06/27
---------------------------

* Add Finnish localization. (#170)
* Add Japanese localization. (#171)
* Add belgian-french localization. (#151)
* Add Czech localization. (#154) 
* Add Thai localization. (#139)
* Improve English localization. (#144) 
* Improve Spanish localization. (#167)
* Improve Italian localization. (#143)
* Improve documentation. (#155, #145, #174)

Version 0.5.6 -- 2017/11/22
---------------------------

* Refactor to_currency (#135)
* Allow the use of other convertes to_currency, to_year (#95)
* Fix code to respect PEP8 (#98, #105)
* Add Slovene localization (#97)
* Add Ukrainian localization (#93)
* Add Dutch localization (#91)
* Add Algeria-French localization (#86)
* Add Turkish localization (#85)

Version 0.5.5 -- 2017/07/02
---------------------------

* Add Arabic localization (#72)
* Add Spanish-Colombian and Spanish-Venezuelan localization (#67)
* Add VietNam localization (#61)
* Add Italian localization (#56, #59)
* Improve Russian localization (#62)
* Improve Polish localization (#58)

Version 0.5.4 -- 2016/10/18
---------------------------

* Tons of new languages!
* Add Polish localization. (#23)
* Add Swiss-French localization. (#38)
* Add Russian localization. (#28, #46, #48)
* Add Indonesian localization. (#29)
* Add Norwegian localization. (#33)
* Add Danish localization. (#40)
* Add Brazilian localization. (#37, #47)
* Improve German localization. (#25, #27, #49)
* Improve Lithuanian localization. (#52)
* Improve floating point spelling. (#24)

Version 0.5.3 -- 2015/06/09
---------------------------

* Fix packaging issues. (#21, #22)

Version 0.5.2 -- 2015/01/23
---------------------------

* Added Latvian localization. (#9)
* Improved Spanish localization. (#10, #13, #14)
* Improved Lithuanian localization. (#12)

Version 0.5.1 -- 2014/03/14
---------------------------

* Added Python 3 support with 2to3. (#3)
* Fixed big numbers in spanish. (#2)
* Fixed bugs in tanslation from 30 to 40 in spanish. (#4)
* Fixed word joining in english. (#8)

Version 0.5.0 -- 2013/05/28
---------------------------

* Created ``num2words`` based on the old ``pynum2word`` project.


