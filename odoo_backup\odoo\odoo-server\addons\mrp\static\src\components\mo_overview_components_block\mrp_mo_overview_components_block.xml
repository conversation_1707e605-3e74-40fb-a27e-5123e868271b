<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mrp.MoOverviewComponentsBlock">
        <t t-foreach="props.components" t-as="component" t-key="component.summary.index">
            <MoOverviewLine
                data="component.summary"
                hasFoldButton="hasReplenishments(component)"
                isFolded="state.fold[component.summary.index]"
                showOptions="props.showOptions"
                toggleFolded.bind="onToggleFolded"/>
            <!-- Display Replenishment block -->
            <t t-if="hasReplenishmentsBlock(component)" t-foreach="component.replenishments" t-as="replenishment" t-key="replenishment.summary.index">
                <MoOverviewLine
                    data="replenishment.summary"
                    hasFoldButton="hasComponents(replenishment)"
                    isFolded="state.fold[replenishment.summary.index]"
                    showOptions="props.showOptions"
                    toggleFolded.bind="onToggleFolded"/>
                <t t-if="hasComponentsBlock(replenishment)">
                    <MoOverviewComponentsBlock
                        unfoldAll="state.unfoldAll"
                        components="replenishment.components"
                        operations="replenishment.operations"
                        byproducts="replenishment.byproducts"
                        showOptions="props.showOptions"/>
                </t>
            </t>
        </t>
        <MoOverviewOperationsBlock
            unfoldAll="state.unfoldAll"
            summary="props.operations.summary"
            operations="props.operations.details"
            showOptions="props.showOptions"/>
        <MoOverviewByproductsBlock
            unfoldAll="state.unfoldAll"
            summary="props.byproducts.summary"
            byproducts="props.byproducts.details"
            showOptions="props.showOptions"/>
    </t>

</templates>
