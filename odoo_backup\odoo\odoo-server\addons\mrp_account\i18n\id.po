# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$1000"
msgstr "$1000"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "$5000"
msgstr "$5000"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
msgid "%s - Labour"
msgstr "%s - Labour"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "2023-08-15"
msgstr "2023-08-15"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_move_form_inherit_mrp_account
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Manufaktur</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">Valuation</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span style=\"margin-right: 15px;\">Total</span>"
msgstr "<span style=\"margin-right: 15px;\">Total</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Amount</span>"
msgstr "<span>Nominal</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Date</span>"
msgstr "<span>Tanggal</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Product</span>"
msgstr "<span>Produk</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Quantity</span>"
msgstr "<span>Jumlah</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Ref.</span>"
msgstr "<span>Referensi.</span>"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "<span>Unit of Measure</span>"
msgstr "<span>Satuan Ukuran</span>"

#. module: mrp_account
#: model:ir.model.constraint,message:mrp_account.constraint_mrp_account_wip_accounting_line_check_debit_credit
msgid "A single line cannot be both credit and debit."
msgstr "Satu baris tidak boleh menjadi kredit dan debit."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__account_id
msgid "Account"
msgstr "Akun"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__account_move_line_id
msgid "Account Move Line"
msgstr "Baris Pergerakkan Akun"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting_line
msgid "Account move line to be created when posting WIP account move"
msgstr ""
"Baris pergerakkan akun untuk dibuat saat memposting pergerakkan akun WIP"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Acme Corp."
msgstr "Acme Corp."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "Akun Analitik"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribusi Analitik"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Baris Analitik"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Penerapan Rencana Analitik"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__analytic_precision
msgid "Analytic Precision"
msgstr "Ketelitian Analitik"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Bills of Materials"
msgstr "Daftar Kebutuhan Material (BOM)"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "Jumlah BoM"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
msgid "Bom"
msgstr "BOM"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Kategori"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "Hitung Harga dari BoM"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Hitung harga produk menggunakan produk dan operasi BoM terkait, hanya untuk "
"produk yang dimanufaktur."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_ids
msgid "Costs Hour Account"
msgstr "Akun Biaya Perjam"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__create_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__credit
msgid "Credit"
msgstr "Kredit"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__date
msgid "Date"
msgstr "Tanggal"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__debit
msgid "Debit"
msgstr "Debit"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Discard"
msgstr "Buang"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Distribution Analytic Account"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Ruang Lingkup"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__expense_account_id
msgid "Expense Account"
msgstr "Akun Beban"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr "Biaya Unit Ekstra"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__id
msgid "ID"
msgstr "ID"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__journal_id
msgid "Journal"
msgstr "Jurnal"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr "Item Jurnal"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__label
msgid "Label"
msgstr "Label"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Laptop"
msgstr "Laptop"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_uid
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__write_date
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Laporan Gambaran Umum MO"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manual Entry"
msgstr "Entri Manual"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_applicability__business_domain__manufacturing_order
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Manufacturing Order"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
msgid "Manufacturing Orders"
msgstr "Manufacturing Orders"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_count
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_count
msgid "Manufacturing Orders Count"
msgstr "Jumlah Manufacturing Order"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Manufacturing WIP - %(orders_list)s"
msgstr "WIP Manufaktur - %(orders_list)s"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__mo_ids
msgid "Mo"
msgstr "Mo"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_ids
msgid "Mo Analytic Account Line"
msgstr "Baris Akun Analitik Mo"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid ""
"Please make sure the total credit amount equals the total debit amount."
msgstr "Mohon pastikkan jumlah kredit total sama dengan jumlah total debit."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post Manufacturing WIP"
msgstr "Post Manufacturing WIP"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Post WIP"
msgstr "Post WIP"

#. module: mrp_account
#: model:ir.actions.act_window,name:mrp_account.action_wip_accounting
msgid "Post WIP Accounting Entry"
msgstr "Post WIP Accounting Entry"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr "Produk"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_category
msgid "Product Category"
msgstr "Kategori Produk"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr "Varian Produk"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
msgid "Production"
msgstr "Produksi"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid "Production Account"
msgstr "Akun Produksi"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "REF123"
msgstr "REF123"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reference
msgid "Reference"
msgstr "Referensi"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,field_description:mrp_account.field_account_move__wip_production_ids
msgid "Relevant WIP MOs"
msgstr "Relevant WIP MOs"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__reversal_date
msgid "Reversal Date"
msgstr "Tanggal Reversal"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal date must be after the posting date."
msgstr "Tanggal reversal harus setlah tanggal posting."

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "Reversal of: %s"
msgstr "Reversal of: %s"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "Tampilkan Penilaian"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Pergerakan Stok"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "Stock Valuation Layer"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_account_bank_statement_line__wip_production_ids
#: model:ir.model.fields,help:mrp_account.field_account_move__wip_production_ids
msgid ""
"The MOs that this WIP entry was based on. Expected to be set at time of WIP "
"entry creation."
msgstr ""
"MO yang merupakan dasar entri WIP ini. Diharapkan untuk ditetapkan pada "
"waktu pembuatan entri WIP."

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__expense_account_id
msgid ""
"The expense is accounted for when the manufacturing order is marked as done."
" If not set, it is the expense account of the final product that will be "
"used instead."
msgstr ""
"Pengeluaran diperhitungkan untuk saat pesanan manufaktur ditandai sebagai "
"selesai. Bila tidak ditandai, akun pengeluaran dari produk final yang alih-"
"alih akan digunakan."

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_product_category__property_stock_account_production_cost_id
msgid ""
"This account will be used as a valuation counterpart for both components and final products for manufacturing orders.\n"
"                If there are any workcenter/employee costs, this value will remain on the account once the production is completed."
msgstr ""
"Akun ini akan digunakan sebagai valuation counterpart untuk komponen dan produk akhir untuk manufacturing order.\n"
"                Bila terdapat biaya pusatkerja/karyawan, nilai ini akan tersisa pada akun setelah produksi selesai."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Credit"
msgstr "Total Kredit"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.view_wip_accounting_form
msgid "Total Debit"
msgstr "Total Debit"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "Units"
msgstr "Unit"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Component Value"
msgstr "WIP - Nilai Komponen"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/wizard/mrp_wip_accounting.py:0
msgid "WIP - Overhead"
msgstr "WIP - Overhead"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/account_move.py:0
msgid "WIP MOs of %s"
msgstr "WIP MOs dari %s"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.report_wip
msgid "WIP Report for"
msgstr "Laporan WIP untuk"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting__line_ids
msgid "WIP accounting lines"
msgstr "Baris akuntansi WIP"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_account_wip_accounting_line__wip_accounting_id
msgid "WIP accounting wizard"
msgstr "Wizard akuntansi WIP"

#. module: mrp_account
#: model:ir.actions.report,name:mrp_account.wip_report
msgid "WIP report"
msgstr "Laporan WIP"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_ids
msgid "Wc Analytic Account Line"
msgstr "Baris Akun Analitik Wc"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_account_wip_accounting
msgid "Wizard to post Manufacturing WIP account move"
msgstr "Wizard untuk posting pergerakkan akun Manufacturing WIP"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "Pusat Kerja"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Penggunaan Pusat Kerja"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "SPK"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "Jumlah SPK"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/analytic_account.py:0
msgid "Work Orders"
msgstr "SPK"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenter"
msgstr "Pusat kerja"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Log Produktivitas Pusat kerja"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
msgid "[WC] %s"
msgstr "[WC] %s"
