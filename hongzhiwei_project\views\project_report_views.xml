<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 鸿之微项目报表视图 -->
    <record id="view_hongzhiwei_project_report_list" model="ir.ui.view">
        <field name="name">hongzhiwei.project.report.list</field>
        <field name="model">project.project</field>
        <field name="arch" type="xml">
            <list string="鸿之微项目报表" decoration-success="project_status=='completed'" 
                  decoration-warning="project_status=='in_progress'" decoration-danger="project_status=='cancelled'">
                <field name="name" string="项目名称"/>
                <field name="project_number" string="项目序号"/>
                <field name="project_status" string="项目状态"/>
                <field name="delivery_person_id" string="项目交付人"/>
                <field name="hongzhiwei_contact_id" string="鸿之微对接人"/>
                <field name="project_category_id" string="项目分类"/>
                <field name="project_manager_id" string="项目负责人"/>
                <field name="team_member_ids" string="项目团队成员" widget="many2many_tags"/>
                <field name="progress_update_time" string="总体进度更新时间"/>
                <field name="deadline" string="截止时间"/>
                <field name="next_progress_update" string="下次总体进度更新时间"/>
                <field name="planned_person_days" string="计划投入人天"/>
                <field name="consumed_person_days" string="已消耗总人天"/>
                <!-- <field name="progress_percentage" widget="percentage" string="进度百分比"/> -->
                <field name="current_milestone" string="里程碑（当前）"/>
                <field name="next_milestone_time" string="下次里程碑时间"/>
                <field name="partner_id" string="客户"/>
            </list>
        </field>
    </record>

    <!-- 鸿之微项目详细表单视图 -->
    <record id="view_hongzhiwei_project_report_form" model="ir.ui.view">
        <field name="name">hongzhiwei.project.report.form</field>
        <field name="model">project.project</field>
        <field name="arch" type="xml">
            <form string="鸿之微项目详情">
                <sheet>
                    <group>
                        <group string="基本信息">
                            <field name="name" string="项目名称"/>
                            <field name="project_number" string="项目序号"/>
                            <field name="project_status" string="项目状态"/>
                            <field name="project_category_id" string="项目分类"/>
                            <field name="partner_id" string="客户"/>
                        </group>
                        <group string="人员信息">
                            <field name="project_manager_id" string="项目负责人"/>
                            <field name="delivery_person_id" string="项目交付人"/>
                            <field name="hongzhiwei_contact_id" string="鸿之微对接人"/>
                            <field name="team_member_ids" string="项目团队成员" widget="many2many_tags"/>
                        </group>
                    </group>
                    
                    <group>
                        <group string="时间管理">
                            <field name="progress_update_time" string="总体进度更新时间"/>
                            <field name="deadline" string="截止时间"/>
                            <field name="next_progress_update" string="下次总体进度更新时间"/>
                        </group>
                        <group string="工作量统计">
                            <field name="planned_person_days" string="计划投入人天"/>
                            <field name="consumed_person_days" string="已消耗总人天"/>
                            <!-- <field name="progress_percentage" widget="percentage" string="进度百分比"/> -->
                        </group>
                    </group>
                    
                    <group>
                        <group string="里程碑管理">
                            <field name="current_milestone" string="里程碑（当前）"/>
                            <field name="next_milestone_time" string="下次里程碑时间"/>
                        </group>
                    </group>
                    
                    <group string="项目描述">
                        <field name="overall_task_description" string="项目总体任务描述" nolabel="1"/>
                    </group>
                    
                    <group>
                        <group string="时间节点">
                            <field name="time_nodes" string="时间节点" nolabel="1"/>
                        </group>
                        <group string="备注">
                            <field name="project_notes" string="备注" nolabel="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- 鸿之微项目搜索视图 -->
    <record id="view_hongzhiwei_project_search" model="ir.ui.view">
        <field name="name">hongzhiwei.project.search</field>
        <field name="model">project.project</field>
        <field name="arch" type="xml">
            <search string="鸿之微项目搜索">
                <field name="project_number" string="项目序号"/>
                <field name="name" string="项目名称"/>
                <field name="project_status" string="项目状态"/>
                <field name="project_category_id" string="项目分类"/>
                <field name="project_manager_id" string="项目负责人"/>
                <field name="delivery_person_id" string="项目交付人"/>
                <field name="hongzhiwei_contact_id" string="鸿之微对接人"/>
                <field name="team_member_ids" string="团队成员"/>
                <separator/>
                <filter string="进行中" name="in_progress" domain="[('project_status', '=', 'in_progress')]"/>
                <filter string="已完成" name="completed" domain="[('project_status', '=', 'completed')]"/>
                <filter string="已交付" name="delivered" domain="[('project_status', '=', 'delivered')]"/>
                <filter string="本月截止" name="deadline_this_month" domain="[('deadline', '&gt;=', (context_today() + relativedelta(day=1)).strftime('%Y-%m-%d')), ('deadline', '&lt;=', (context_today() + relativedelta(months=1, day=1, days=-1)).strftime('%Y-%m-%d'))]"/>
                <separator/>
                <group expand="0" string="分组">
                    <filter string="项目状态" name="group_status" context="{'group_by': 'project_status'}"/>
                    <filter string="项目分类" name="group_category" context="{'group_by': 'project_category_id'}"/>
                    <filter string="项目负责人" name="group_manager" context="{'group_by': 'project_manager_id'}"/>
                    <filter string="项目交付人" name="group_delivery" context="{'group_by': 'delivery_person_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 鸿之微项目菜单动作 -->
    <record id="action_hongzhiwei_project_report" model="ir.actions.act_window">
        <field name="name">鸿之微项目报表</field>
        <field name="res_model">project.project</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_hongzhiwei_project_report_list"/>
        <field name="search_view_id" ref="view_hongzhiwei_project_search"/>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建您的第一个鸿之微项目！
            </p>
        </field>
    </record>

    <!-- 添加到项目菜单 -->
    <menuitem id="menu_hongzhiwei_project_report"
              name="鸿之微项目报表"
              parent="project.menu_main_pm"
              action="action_hongzhiwei_project_report"
              sequence="15"/>
</odoo>
