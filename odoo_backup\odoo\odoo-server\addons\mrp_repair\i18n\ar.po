# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_repair
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mrp_repair
#: model:ir.model.fields,field_description:mrp_repair.field_repair_order__production_count
msgid "Count of MOs generated"
msgstr "عدد أوامر التصنيع المنشأة "

#. module: mrp_repair
#: model:ir.model.fields,field_description:mrp_repair.field_mrp_production__repair_count
msgid "Count of source repairs"
msgstr "عدد عمليات التصليح المصدرية "

#. module: mrp_repair
#: model_terms:ir.ui.view,arch_db:mrp_repair.view_repair_order_form_inherit
msgid "Manufacturing"
msgstr "التصنيع"

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_mrp_production
msgid "Manufacturing Order"
msgstr "أمر التصنيع "

#. module: mrp_repair
#. odoo-python
#: code:addons/mrp_repair/models/repair.py:0
msgid "Manufacturing Orders generated by %s"
msgstr "طلبات التصنيع التي تم إنشاؤها بواسطة %s"

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_repair_order
msgid "Repair Order"
msgstr "أمر الإصلاح "

#. module: mrp_repair
#. odoo-python
#: code:addons/mrp_repair/models/production.py:0
msgid "Repair Source of %s"
msgstr "مصدر التصليح لـ %s "

#. module: mrp_repair
#: model_terms:ir.ui.view,arch_db:mrp_repair.mrp_production_form_view_inherit
msgid "Repairs"
msgstr "الإصلاحات "

#. module: mrp_repair
#: model:ir.model,name:mrp_repair.model_stock_move
msgid "Stock Move"
msgstr "حركة المخزون"
